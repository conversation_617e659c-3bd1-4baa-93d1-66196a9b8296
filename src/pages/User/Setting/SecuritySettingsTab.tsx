import React, { useState } from "react";
import { FaEye, FaEyeSlash, FaShieldAlt } from "react-icons/fa";
import { userApi } from "@/services/api";
import { toast } from "react-toastify";

const SecuritySettingsTab: React.FC = () => {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");

  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

  const [passwordStrength, setPasswordStrength] = useState(0);
  const [strengthText, setStrengthText] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [errors, setErrors] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmNewPassword?: string;
  }>({});

  const passwordRequirements = [
    {
      id: "length",
      text: "At least 8 characters",
      met: newPassword.length >= 8,
    },
    {
      id: "uppercase",
      text: "At least one uppercase letter",
      met: /[A-Z]/.test(newPassword),
    },
    {
      id: "lowercase",
      text: "At least one lowercase letter",
      met: /[a-z]/.test(newPassword),
    },
    {
      id: "number",
      text: "At least one number",
      met: /[0-9]/.test(newPassword),
    },
    {
      id: "special",
      text: "At least one special character (e.g., !?$)",
      met: /[^A-Za-z0-9]/.test(newPassword),
    },
  ];

  const calculateStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    setPasswordStrength(strength);

    if (strength <= 2) setStrengthText("Weak");
    else if (strength <= 4) setStrengthText("Medium");
    else setStrengthText("Strong");
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setNewPassword(val);
    calculateStrength(val);
    // Clear error when typing
    if (errors.newPassword) {
      setErrors((prev) => ({ ...prev, newPassword: undefined }));
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const val = e.target.value;
    setConfirmNewPassword(val);
    // Clear error when typing
    if (errors.confirmNewPassword) {
      setErrors((prev) => ({ ...prev, confirmNewPassword: undefined }));
    }
  };

  const handleCurrentPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const val = e.target.value;
    setCurrentPassword(val);
    // Clear error when typing
    if (errors.currentPassword) {
      setErrors((prev) => ({ ...prev, currentPassword: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};
    let isValid = true;

    if (!currentPassword) {
      newErrors.currentPassword = "Current password is required";
      isValid = false;
    }

    if (!newPassword) {
      newErrors.newPassword = "New password is required";
      isValid = false;
    } else if (passwordStrength < 3) {
      newErrors.newPassword = "Password is too weak";
      isValid = false;
    }

    if (!confirmNewPassword) {
      newErrors.confirmNewPassword = "Please confirm your new password";
      isValid = false;
    } else if (newPassword !== confirmNewPassword) {
      newErrors.confirmNewPassword = "Passwords do not match";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleUpdatePassword = async () => {
    if (!validateForm()) return;

    setIsUpdating(true);
    try {
      const response = await userApi.changePassword({
        currentPassword,
        newPassword,
        confirmPassword: confirmNewPassword,
      });

      if (response.success) {
        toast.success("Password updated successfully");
        // Reset form on success
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
        setPasswordStrength(0);
        setStrengthText("");
      } else {
        toast.error(response.message || "Failed to update password");
        // Set error if current password is incorrect
        if (response.message?.includes("incorrect")) {
          setErrors((prev) => ({
            ...prev,
            currentPassword: "Current password is incorrect",
          }));
        }
      }
    } catch (error) {
      console.error("Error updating password:", error);
      toast.error("An error occurred while updating your password");
    } finally {
      setIsUpdating(false);
    }
  };

  const getStrengthBarColor = () => {
    if (passwordStrength <= 2) return "bg-red-500";
    if (passwordStrength <= 4) return "bg-yellow-500";
    return "bg-green-500";
  };

  const handleCancel = () => {
    // Reset form
    setCurrentPassword("");
    setNewPassword("");
    setConfirmNewPassword("");
    setPasswordStrength(0);
    setStrengthText("");
    setErrors({});
  };

  return (
    <div className="space-y-8">
      <div>
        <div className="flex items-center mb-1">
          <FaShieldAlt className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Security</h2>
        </div>
        <p className="text-sm text-gray-600">
          Manage your account security settings to enhance the protection of
          your personal data.
        </p>
      </div>

      <div className="bg-white p-6 sm:p-8 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">
          Change Your Password
        </h3>

        <div className="space-y-6">
          <div>
            <label
              htmlFor="currentPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Current Password
            </label>
            <div className="relative">
              <input
                type={showCurrentPassword ? "text" : "password"}
                id="currentPassword"
                value={currentPassword}
                onChange={handleCurrentPasswordChange}
                placeholder="Enter current password"
                className={`w-full p-2.5 border ${
                  errors.currentPassword ? "border-red-500" : "border-gray-300"
                } rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm`}
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
              >
                {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.currentPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.currentPassword}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="newPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              New Password
            </label>
            <div className="relative">
              <input
                type={showNewPassword ? "text" : "password"}
                id="newPassword"
                value={newPassword}
                onChange={handleNewPasswordChange}
                placeholder="Enter new password"
                className={`w-full p-2.5 border ${
                  errors.newPassword ? "border-red-500" : "border-gray-300"
                } rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm`}
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
              >
                {showNewPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.newPassword && (
              <p className="mt-1 text-xs text-red-500">{errors.newPassword}</p>
            )}
            {newPassword.length > 0 && (
              <div className="mt-2">
                <div className="flex justify-between items-center mb-1">
                  <p className="text-xs text-gray-500">Password strength:</p>
                  <p
                    className={`text-xs font-medium ${passwordStrength <= 2 ? "text-red-500" : passwordStrength <= 4 ? "text-yellow-600" : "text-green-600"}`}
                  >
                    {strengthText}
                  </p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full ${getStrengthBarColor()}`}
                    style={{ width: `${(passwordStrength / 5) * 100}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <p className="text-sm font-medium text-gray-700 mb-2">
              Your password must include:
            </p>
            <ul className="space-y-1.5">
              {passwordRequirements.map((req) => (
                <li
                  key={req.id}
                  className={`flex items-center text-xs ${req.met ? "text-green-600" : "text-gray-500"}`}
                >
                  <input
                    type="radio"
                    checked={req.met}
                    readOnly
                    className={`mr-2 form-radio h-3 w-3 ${req.met ? "text-green-500 border-green-500" : "text-gray-400 border-gray-300"} focus:ring-transparent focus:ring-offset-0`}
                  />
                  {req.text}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <label
              htmlFor="confirmNewPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Confirm New Password
            </label>
            <div className="relative">
              <input
                type={showConfirmNewPassword ? "text" : "password"}
                id="confirmNewPassword"
                value={confirmNewPassword}
                onChange={handleConfirmPasswordChange}
                placeholder="Confirm new password"
                className={`w-full p-2.5 border ${
                  errors.confirmNewPassword
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm`}
              />
              <button
                type="button"
                onClick={() =>
                  setShowConfirmNewPassword(!showConfirmNewPassword)
                }
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
              >
                {showConfirmNewPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.confirmNewPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.confirmNewPassword}
              </p>
            )}
          </div>
        </div>

        <div className="mt-8 pt-5 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleUpdatePassword}
            disabled={isUpdating}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isUpdating ? (
              <div className="flex items-center">
                <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                Updating...
              </div>
            ) : (
              "Update Password"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettingsTab;
