import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import {
  FaSearch,
  FaChevronDown,
  FaChevronRight,
  FaEdit,
  FaTrash,
  FaPlus,
  FaTimes,
} from "react-icons/fa";
import { Link } from "react-router-dom";

interface BonusTracker {
  id: string;
  cardName: string;
  cardType: string; // This represents the card type like "Preferred", "Card" etc.
  cardIcon: string;
  cardColor: string;
  bonusType?: string; // Make this optional since it might not be present in all data
  bonusAmount: string;
  bonusUnit: string;
  spendRequired: string;
  endDate: string;
  status: "In Progress" | "Complete" | "Expired";
  progress: {
    spent: number;
    required: number;
  };
}

interface AddTrackerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (tracker: Partial<BonusTracker>) => void;
}

const AddTrackerModal: React.FC<AddTrackerModalProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const [trackerType, setTrackerType] = useState("Sign-Up Bonus");
  const [selectedCard, setSelectedCard] = useState("");
  const [bonusAmount, setBonusAmount] = useState("200");
  const [spendRequired, setSpendRequired] = useState("3,000");
  const [approvalDate, setApprovalDate] = useState("2025-05-13");
  const [duration, setDuration] = useState("3 months");
  const [endDate, setEndDate] = useState("2025-11-13");
  const [additionalNotes, setAdditionalNotes] = useState("");
  const [currentSpending, setCurrentSpending] = useState("0");

  if (!isOpen) return null;

  const getCalculatedEndDate = () => {
    if (!approvalDate) return "";

    try {
      const date = new Date(approvalDate);
      const months = parseInt(duration.split(" ")[0], 10) || 3;
      date.setMonth(date.getMonth() + months);
      return date.toISOString().split("T")[0];
    } catch (e) {
      return "";
    }
  };

  const remaining = spendRequired
    ? parseInt(spendRequired.replace(/,/g, "")) -
      parseInt(currentSpending || "0")
    : 0;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl overflow-hidden">
        <div className="flex justify-between items-center p-3 md:p-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-800">
            Add New Bonus Tracker
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <FaTimes />
          </button>
        </div>

        <div className="p-4 md:p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 md:gap-x-8 gap-y-4 md:gap-y-3">
            <div className="col-span-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Tracker Type
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                <button
                  className={`px-3 py-2 text-sm rounded-md w-full ${
                    trackerType === "Sign-Up Bonus"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                  onClick={() => setTrackerType("Sign-Up Bonus")}
                >
                  Sign-Up Bonus
                </button>
                <button
                  className={`px-3 py-2 text-sm rounded-md w-full ${
                    trackerType === "Retention Bonus"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                  onClick={() => setTrackerType("Retention Bonus")}
                >
                  Retention Bonus
                </button>
                <button
                  className={`px-3 py-2 text-sm rounded-md w-full ${
                    trackerType === "Transaction Bonus"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                  onClick={() => setTrackerType("Transaction Bonus")}
                >
                  Transaction Bonus
                </button>
              </div>
            </div>

            <div className="col-span-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Card
              </label>
              <div className="relative">
                <select
                  className="w-full appearance-none rounded-md border border-gray-300 py-2 pl-3 pr-10 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={selectedCard}
                  onChange={(e) => setSelectedCard(e.target.value)}
                >
                  <option value="">Select a card from your account</option>
                  <option value="Chase Sapphire">Chase Sapphire</option>
                  <option value="Amex Gold">Amex Gold</option>
                  <option value="Citi Double Cash">Citi Double Cash</option>
                  <option value="Capital One Venture">
                    Capital One Venture
                  </option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg
                    className="h-4 w-4 fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bonus Amount
              </label>
              <div className="relative rounded-md">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="text"
                  value={bonusAmount}
                  onChange={(e) => setBonusAmount(e.target.value)}
                  className="w-full rounded-md border border-gray-300 py-2 pl-7 pr-3 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Spend Required
              </label>
              <div className="relative rounded-md">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="text"
                  value={spendRequired}
                  onChange={(e) => setSpendRequired(e.target.value)}
                  className="w-full rounded-md border border-gray-300 py-2 pl-7 pr-3 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Approval Date
              </label>
              <input
                type="date"
                value={approvalDate}
                onChange={(e) => {
                  setApprovalDate(e.target.value);
                  const calculated = getCalculatedEndDate();
                  if (calculated) setEndDate(calculated);
                }}
                className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration
              </label>
              <div className="relative">
                <select
                  value={duration}
                  onChange={(e) => {
                    setDuration(e.target.value);
                    const calculated = getCalculatedEndDate();
                    if (calculated) setEndDate(calculated);
                  }}
                  className="w-full appearance-none rounded-md border border-gray-300 py-2 pl-3 pr-10 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="1 month">1 month</option>
                  <option value="2 months">2 months</option>
                  <option value="3 months">3 months</option>
                  <option value="6 months">6 months</option>
                  <option value="12 months">12 months</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg
                    className="h-4 w-4 fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            <div className="col-span-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Notes (Optional)
              </label>
              <textarea
                value={additionalNotes}
                onChange={(e) => setAdditionalNotes(e.target.value)}
                placeholder="Add any important details about this bonus..."
                className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={2}
              />
            </div>

            <div className="col-span-1 md:col-span-2 bg-gray-50 p-3 rounded-md">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Current Progress
              </h4>
              <div className="flex justify-between text-sm text-gray-600 mb-3">
                <div>
                  Current Spending: ${currentSpending} of ${spendRequired}{" "}
                  required
                </div>
                <div>${remaining.toLocaleString()} remaining</div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">
                  Add Spending (Optional)
                </div>
                <div className="flex space-x-2">
                  <div className="relative rounded-md flex-shrink-0">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input
                      type="text"
                      value={currentSpending}
                      onChange={(e) => setCurrentSpending(e.target.value)}
                      className="w-56 rounded-md border border-gray-300 py-2 pl-7 pr-3 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md text-sm">
                    Update Progress
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-3 md:p-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
          <button
            onClick={() => {
              onSave({
                id: Math.random().toString(36).substring(7),
                cardName: selectedCard,
                bonusType: trackerType,
                bonusAmount: bonusAmount,
                bonusUnit: "points",
                spendRequired: `$${spendRequired}`,
                endDate: new Date(endDate).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                }),
                cardType: trackerType,
                status: "In Progress",
                progress: {
                  spent: parseInt(currentSpending || "0"),
                  required: parseInt(spendRequired.replace(/,/g, "") || "3000"),
                },
              });
              onClose();
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 w-full sm:w-auto"
          >
            Save Tracker
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 w-full sm:w-auto"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

const BonusTrackerPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"Active" | "Expired">("Active");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCardType, setFilterCardType] = useState("All Cards");
  const [filterBonusType, setFilterBonusType] = useState("All Bonuses");
  const [trackers, setTrackers] = useState<BonusTracker[]>([
    {
      id: "1",
      cardName: "Chase Sapphire Preferred",
      cardType: "Sign-Up Bonus",
      cardIcon: "visa",
      cardColor: "bg-blue-600",
      bonusAmount: "60,000",
      bonusUnit: "points",
      spendRequired: "$3,000",
      endDate: "May 20, 2025",
      status: "In Progress",
      progress: {
        spent: 2400,
        required: 3000,
      },
    },
    {
      id: "2",
      cardName: "American Express Gold",
      cardType: "Sign-Up Bonus",
      cardIcon: "amex",
      cardColor: "bg-amber-500",
      bonusAmount: "75,000",
      bonusUnit: "points",
      spendRequired: "$4,000",
      endDate: "June 15, 2025",
      status: "In Progress",
      progress: {
        spent: 1200,
        required: 4000,
      },
    },
    {
      id: "3",
      cardName: "Capital One Venture",
      cardType: "Sign-Up Bonus",
      cardIcon: "mastercard",
      cardColor: "bg-blue-600",
      bonusAmount: "75,000",
      bonusUnit: "miles",
      spendRequired: "$4,000",
      endDate: "July 30, 2025",
      status: "Complete",
      progress: {
        spent: 4000,
        required: 4000,
      },
    },
  ]);

  const handleAddTracker = (tracker: Partial<BonusTracker>) => {
    // Generate card icon from card name
    const cardIcon = tracker.cardName?.toLowerCase().includes("amex")
      ? "amex"
      : tracker.cardName?.toLowerCase().includes("chase")
        ? "visa"
        : tracker.cardName?.toLowerCase().includes("capital")
          ? "mastercard"
          : "credit-card";

    // Get card color based on card name
    let cardColor = "bg-gray-500";
    if (tracker.cardName?.includes("Chase")) cardColor = "bg-blue-600";
    if (tracker.cardName?.includes("Amex")) cardColor = "bg-amber-500";
    if (tracker.cardName?.includes("Capital")) cardColor = "bg-blue-600";

    // Create the new tracker object with provided and default values
    const newTracker: BonusTracker = {
      id: tracker.id || Math.random().toString(),
      cardName: tracker.cardName || "New Card",
      cardType: tracker.cardType || "Sign-Up Bonus",
      cardIcon: cardIcon,
      cardColor: cardColor,
      bonusType: tracker.bonusType,
      bonusAmount: tracker.bonusAmount || "50,000",
      bonusUnit: tracker.bonusUnit || "points",
      spendRequired: tracker.spendRequired || "$3,000",
      endDate: tracker.endDate || "Dec 31, 2025",
      status: "In Progress",
      progress: {
        spent: tracker.progress?.spent || 0,
        required: tracker.progress?.required || 3000,
      },
    };

    setTrackers([...trackers, newTracker]);
  };

  return (
    <UserLayout>
      <div className="container mx-auto px-0 sm:px-4 py-4 sm:py-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">
              Bonus Tracker
            </h1>
            <p className="text-gray-600 text-sm">
              Track and manage your credit card bonus offers and spending
              requirements.
            </p>
          </div>
          <button
            onClick={() => setIsModalOpen(true)}
            className="mt-3 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-blue-700 w-full sm:w-auto justify-center"
          >
            <FaPlus className="mr-2" />
            Add New Tracker
          </button>
        </div>

        {/* Filters and Search */}
        <div className="mb-6 p-4 bg-white rounded-lg shadow">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            <div>
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Search Trackers
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by card name, bonus..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <label
                htmlFor="cardType"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Card Type
              </label>
              <div className="relative">
                <select
                  id="cardType"
                  value={filterCardType}
                  onChange={(e) => setFilterCardType(e.target.value)}
                  className="w-full appearance-none pl-3 pr-10 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
                >
                  <option>All Cards</option>
                  <option>Chase Sapphire</option>
                  <option>Amex Gold</option>
                  <option>Citi Double Cash</option>
                  <option>Discover It</option>
                  <option>Capital One Venture</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <FaChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
            <div>
              <label
                htmlFor="bonusType"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Bonus Type
              </label>
              <div className="relative">
                <select
                  id="bonusType"
                  value={filterBonusType}
                  onChange={(e) => setFilterBonusType(e.target.value)}
                  className="w-full appearance-none pl-3 pr-10 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
                >
                  <option>All Bonuses</option>
                  <option>Sign-Up Bonus</option>
                  <option>Retention Bonus</option>
                  <option>Spending Bonus</option>
                  <option>Referral Bonus</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <FaChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
            <button className="bg-white text-blue-600 border border-blue-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-50 w-full lg:w-auto justify-center">
              Reset Filters
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-5">
          <div className="flex space-x-8">
            <button
              className={`pb-3 text-sm font-medium ${
                activeTab === "Active"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("Active")}
            >
              Active
            </button>
            <button
              className={`pb-3 text-sm font-medium ${
                activeTab === "Expired"
                  ? "text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("Expired")}
            >
              Expired
            </button>
          </div>
        </div>

        {/* Active Trackers Section */}
        <div className="mb-8">
          <div className="flex items-center mb-5">
            <div className="bg-blue-100 rounded-full p-1.5 mr-2">
              <svg
                className="w-4 h-4 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            </div>
            <h2 className="text-lg font-medium text-gray-800">
              Active Bonus Trackers
            </h2>
          </div>

          {/* Tracker Cards */}
          <div className="space-y-5">
            {trackers
              .filter((t) =>
                activeTab === "Active"
                  ? t.status !== "Expired"
                  : t.status === "Expired"
              )
              .map((tracker) => (
                <div
                  key={tracker.id}
                  className={`${tracker.status === "Complete" ? "border border-gray-200 rounded-lg overflow-hidden" : "bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200"}`}
                >
                  <Link
                    to={`/user/rewards/cards/${tracker.id}`}
                    className="block cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex flex-col md:flex-row">
                      <div className="p-5 flex items-start space-x-4 flex-grow">
                        <div
                          className={`flex-shrink-0 w-12 h-12 ${tracker.cardColor} rounded-lg flex items-center justify-center text-white`}
                        >
                          {tracker.cardIcon === "visa" && (
                            <svg
                              className="w-8 h-8"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M9 15H7L5 7H7L9 15ZM13 7H11L9 15H11L13 7ZM19 7H17L15 15H17L19 7ZM19 5H5C3.89 5 3 5.89 3 7V17C3 18.11 3.89 19 5 19H19C20.11 19 21 18.11 21 17V7C21 5.89 20.11 5 19 5Z" />
                            </svg>
                          )}
                          {tracker.cardIcon === "amex" && (
                            <svg
                              className="w-8 h-8"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M12 10.5H8V12.5H12V10.5ZM16 10.5H12.5V12.5H16V10.5ZM3 5V19H21V5H3ZM9.5 14.5H7.5V13H6V14.5H4V10H6V11.5H7.5V10H9.5V14.5ZM13.5 14.5H17V12.5H13.5V11.5H17V10H12V14.5H13.5ZM19.5 14.5H17.5V13H16V14.5H14V10H16V11.5H17.5V10H19.5V14.5Z" />
                            </svg>
                          )}
                          {tracker.cardIcon === "mastercard" && (
                            <svg
                              className="w-8 h-8"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 5C13.93 5 15.68 5.78 16.95 7.05L15.53 8.47C14.55 7.5 13.32 6.88 12 6.88C10.68 6.88 9.45 7.5 8.47 8.47L7.05 7.05C8.32 5.78 10.07 5 12 5ZM12 19C10.07 19 8.32 18.22 7.05 16.95L8.47 15.53C9.45 16.5 10.68 17.12 12 17.12C13.32 17.12 14.55 16.5 15.53 15.53L16.95 16.95C15.68 18.22 13.93 19 12 19Z" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-grow">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-800">
                                {tracker.cardName}
                              </h3>
                              <p className="text-sm text-gray-500">
                                {tracker.cardType}
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <button className="text-gray-400 hover:text-blue-500">
                                <svg
                                  className="w-5 h-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                                  />
                                </svg>
                              </button>
                              <button className="text-gray-400 hover:text-red-500">
                                <svg
                                  className="w-5 h-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-6 mt-4">
                            <div>
                              <p className="text-xs text-gray-500 mb-1">
                                Bonus Amount
                              </p>
                              <p className="font-medium text-gray-800">
                                {tracker.bonusAmount} {tracker.bonusUnit}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500 mb-1">
                                Spend Required
                              </p>
                              <p className="font-medium text-gray-800">
                                {tracker.spendRequired}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500 mb-1">
                                End Date
                              </p>
                              <p className="font-medium text-gray-800">
                                {tracker.endDate}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col items-end ml-4 mt-1">
                          <div
                            className={`px-3 py-1 rounded-full text-xs font-medium mb-4 ${
                              tracker.status === "Complete"
                                ? "bg-green-100 text-green-800"
                                : "bg-blue-100 text-blue-800"
                            }`}
                          >
                            {tracker.status === "Complete"
                              ? "Complete"
                              : "In Progress"}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="px-5 pb-5">
                      <div className="flex justify-between text-sm mb-1.5">
                        <div className="text-gray-600">
                          Progress: ${tracker.progress.spent.toLocaleString()}{" "}
                          spent of ${tracker.progress.required.toLocaleString()}{" "}
                          required
                        </div>
                        <div
                          className={
                            tracker.status === "Complete"
                              ? "text-green-600 font-medium"
                              : "text-gray-600 font-medium"
                          }
                        >
                          {tracker.status === "Complete"
                            ? "Requirement met!"
                            : `$${(tracker.progress.required - tracker.progress.spent).toLocaleString()} remaining`}
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            tracker.status === "Complete"
                              ? "bg-green-500"
                              : "bg-blue-500"
                          }`}
                          style={{
                            width: `${Math.min(
                              100,
                              (tracker.progress.spent /
                                tracker.progress.required) *
                                100
                            )}%`,
                          }}
                        ></div>
                      </div>

                      {tracker.status === "Complete" ? (
                        <div className="text-green-600 text-sm mt-2">
                          Congratulations! You've met the spending requirement.
                          Your bonus should post within 1-2 billing cycles.
                        </div>
                      ) : (
                        <div className="text-gray-500 text-sm mt-2">
                          Spend $
                          {(
                            tracker.progress.required - tracker.progress.spent
                          ).toLocaleString()}{" "}
                          more by {tracker.endDate} to earn your bonus.
                        </div>
                      )}
                    </div>
                  </Link>
                </div>
              ))}
          </div>
        </div>

        <AddTrackerModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddTracker}
        />
      </div>
    </UserLayout>
  );
};

export default BonusTrackerPage;
