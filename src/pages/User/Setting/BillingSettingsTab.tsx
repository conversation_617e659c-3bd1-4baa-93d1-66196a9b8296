import React, { useState, useEffect, useCallback } from "react";
import { FaCreditCard, FaHistory, FaCrown, FaSpinner } from "react-icons/fa";
import {
  billingApi,
  SubscriptionPlan,
  PaymentMethod,
  Subscription,
  BillingTransaction,
  BillingHistoryPagination,
} from "@/services/api";
import { toast } from "react-toastify";
import StripeProvider from "@/components/billing/StripeProvider";
import PaymentMethodForm from "@/components/billing/PaymentMethodForm";
import PaymentMethodsList from "@/components/billing/PaymentMethodsList";
import SubscriptionPlans from "@/components/billing/SubscriptionPlans";
import BillingHistory from "@/components/billing/BillingHistory";
import AvailablePlans from "@/components/billing/AvailablePlans";

const BillingSettingsTab: React.FC = () => {
  // States
  const [activeTab, setActiveTab] = useState<
    "payment" | "subscription" | "history"
  >("subscription");
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [showSubscribePlans, setShowSubscribePlans] = useState(false);
  const [currentSubscription, setCurrentSubscription] =
    useState<Subscription | null>(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionPlan[]
  >([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [transactions, setTransactions] = useState<BillingTransaction[]>([]);
  const [historyPagination, setHistoryPagination] =
    useState<BillingHistoryPagination>({
      page: 1,
      limit: 10,
      totalCount: 0,
      totalPages: 0,
      hasMore: false,
    });

  // Loading states
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(true);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isProcessingSubscription, setIsProcessingSubscription] =
    useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [isResuming, setIsResuming] = useState(false);

  // Fetch current subscription
  const fetchCurrentSubscription = useCallback(async () => {
    setIsLoadingSubscription(true);
    try {
      const response = await billingApi.getCurrentSubscription();
      if (response.success) {
        setCurrentSubscription(response.subscription);
      } else {
        toast.error("Failed to load subscription information");
      }
    } catch (error) {
      console.error("Error fetching subscription:", error);
      toast.error("An error occurred while fetching subscription information");
    } finally {
      setIsLoadingSubscription(false);
    }
  }, []);

  // Fetch subscription plans
  const fetchSubscriptionPlans = useCallback(async () => {
    setIsLoadingPlans(true);
    try {
      const response = await billingApi.getSubscriptionPlans();
      if (response.success) {
        setSubscriptionPlans(response.plans);
      } else {
        toast.error("Failed to load subscription plans");
      }
    } catch (error) {
      console.error("Error fetching subscription plans:", error);
      toast.error("An error occurred while fetching subscription plans");
    } finally {
      setIsLoadingPlans(false);
    }
  }, []);

  // Fetch payment methods
  const fetchPaymentMethods = useCallback(async () => {
    setIsLoadingPaymentMethods(true);
    try {
      const response = await billingApi.getPaymentMethods();
      if (response.success) {
        setPaymentMethods(response.paymentMethods);
      } else {
        toast.error("Failed to load payment methods");
      }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      toast.error("An error occurred while fetching payment methods");
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  }, []);

  // Fetch billing history
  const fetchBillingHistory = useCallback(async (page = 1) => {
    setIsLoadingHistory(true);
    try {
      const response = await billingApi.getBillingHistory(page);
      if (response.success) {
        if (page === 1) {
          setTransactions(response.transactions);
        } else {
          setTransactions((prev) => [...prev, ...response.transactions]);
        }
        setHistoryPagination(response.pagination);
      } else {
        toast.error("Failed to load billing history");
      }
    } catch (error) {
      console.error("Error fetching billing history:", error);
      toast.error("An error occurred while fetching billing history");
    } finally {
      setIsLoadingHistory(false);
    }
  }, []);

  // Load data on component mount
  useEffect(() => {
    console.log("Loading subscription data...");
    fetchCurrentSubscription();
    fetchSubscriptionPlans();
    fetchPaymentMethods();

    if (activeTab === "history") {
      fetchBillingHistory();
    }
  }, [
    fetchCurrentSubscription,
    fetchSubscriptionPlans,
    fetchPaymentMethods,
    fetchBillingHistory,
    activeTab,
  ]);

  // Handle tab change
  const handleTabChange = (tab: "payment" | "subscription" | "history") => {
    setActiveTab(tab);
    if (tab === "history" && transactions.length === 0) {
      fetchBillingHistory();
    }
  };

  // Handle subscription creation
  const handleCreateSubscription = async (
    planName: string,
    billingCycle: string,
    paymentMethodId: number
  ) => {
    setIsProcessingSubscription(true);
    try {
      const response = await billingApi.createSubscription(
        planName,
        billingCycle,
        paymentMethodId
      );
      if (response.success) {
        toast.success("Subscription created successfully");
        setShowSubscribePlans(false);
        fetchCurrentSubscription();
      } else {
        toast.error(response.message || "Failed to create subscription");
      }
    } catch (error: any) {
      console.error("Error creating subscription:", error);
      toast.error(
        error.message || "An error occurred while creating subscription"
      );
    } finally {
      setIsProcessingSubscription(false);
    }
  };

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!currentSubscription) return;

    const confirmCancel = window.confirm(
      "Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period."
    );

    if (!confirmCancel) return;

    setIsCanceling(true);
    try {
      const response = await billingApi.cancelSubscription(
        currentSubscription.id,
        true
      );
      if (response.success) {
        toast.success(
          "Subscription will be canceled at the end of your billing period"
        );
        fetchCurrentSubscription();
      } else {
        toast.error(response.message || "Failed to cancel subscription");
      }
    } catch (error: any) {
      console.error("Error canceling subscription:", error);
      toast.error(
        error.message || "An error occurred while canceling subscription"
      );
    } finally {
      setIsCanceling(false);
    }
  };

  // Handle subscription resumption
  const handleResumeSubscription = async () => {
    if (!currentSubscription) return;

    setIsResuming(true);
    try {
      const response = await billingApi.resumeSubscription(
        currentSubscription.id
      );
      if (response.success) {
        toast.success("Subscription resumed successfully");
        fetchCurrentSubscription();
      } else {
        toast.error(response.message || "Failed to resume subscription");
      }
    } catch (error: any) {
      console.error("Error resuming subscription:", error);
      toast.error(
        error.message || "An error occurred while resuming subscription"
      );
    } finally {
      setIsResuming(false);
    }
  };

  // Loading state
  if (
    (activeTab === "subscription" &&
      (isLoadingSubscription || isLoadingPlans)) ||
    (activeTab === "payment" && isLoadingPaymentMethods)
  ) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-blue-500 text-4xl" />
      </div>
    );
  }

  // Debug logging
  console.log("Current subscription:", currentSubscription);
  console.log("Subscription plans:", subscriptionPlans);
  console.log("Loading states:", {
    isLoadingSubscription,
    isLoadingPlans,
    isLoadingPaymentMethods,
  });

  return (
    <div className="space-y-8">
      <div>
        <div className="flex items-center mb-1">
          <FaCreditCard className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Billing</h2>
        </div>
        <p className="text-sm text-gray-600">
          Manage your subscription plan, billing preferences, and payment
          methods
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => handleTabChange("subscription")}
            className={`inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === "subscription"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
          >
            <FaCrown
              className={`mr-2 h-4 w-4 ${activeTab === "subscription" ? "text-blue-500" : "text-gray-400"}`}
            />
            Subscription
          </button>

          <button
            onClick={() => handleTabChange("payment")}
            className={`inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === "payment"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
          >
            <FaCreditCard
              className={`mr-2 h-4 w-4 ${activeTab === "payment" ? "text-blue-500" : "text-gray-400"}`}
            />
            Payment Methods
          </button>

          <button
            onClick={() => handleTabChange("history")}
            className={`inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === "history"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
          >
            <FaHistory
              className={`mr-2 h-4 w-4 ${activeTab === "history" ? "text-blue-500" : "text-gray-400"}`}
            />
            Billing History
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {/* Subscription Tab */}
        {activeTab === "subscription" && (
          <div className="space-y-6">
            {showSubscribePlans ? (
              <StripeProvider>
                <SubscriptionPlans
                  plans={subscriptionPlans}
                  paymentMethods={paymentMethods}
                  onSubscribe={handleCreateSubscription}
                  isProcessing={isProcessingSubscription}
                />
              </StripeProvider>
            ) : (
              <AvailablePlans
                currentSubscription={currentSubscription}
                plans={subscriptionPlans}
                onUpgrade={() => setShowSubscribePlans(true)}
                onCancel={handleCancelSubscription}
                onResume={handleResumeSubscription}
              />
            )}
          </div>
        )}

        {/* Payment Methods Tab */}
        {activeTab === "payment" && (
          <div className="space-y-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-semibold text-gray-800">
                Payment Methods
              </h3>
              <button
                onClick={() => setShowAddPaymentMethod(!showAddPaymentMethod)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm"
              >
                {showAddPaymentMethod ? "Cancel" : "Add Payment Method"}
              </button>
            </div>

            {showAddPaymentMethod ? (
              <StripeProvider>
                <PaymentMethodForm
                  onSuccess={() => {
                    setShowAddPaymentMethod(false);
                    fetchPaymentMethods();
                  }}
                />
              </StripeProvider>
            ) : (
              <PaymentMethodsList
                paymentMethods={paymentMethods}
                onRefresh={fetchPaymentMethods}
                isRefreshing={isLoadingPaymentMethods}
              />
            )}
          </div>
        )}

        {/* Billing History Tab */}
        {activeTab === "history" && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Billing History
            </h3>
            <BillingHistory
              transactions={transactions}
              pagination={historyPagination}
              isLoading={isLoadingHistory}
              onLoadMore={() => fetchBillingHistory(historyPagination.page + 1)}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default BillingSettingsTab;
