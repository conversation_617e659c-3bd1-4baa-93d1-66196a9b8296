import React from "react";
import { Subscription, SubscriptionPlan } from "@/services/api";
import { FaCheck, FaTimes, FaUndo, FaSpinner } from "react-icons/fa";

interface AvailablePlansProps {
  currentSubscription: Subscription | null;
  plans: SubscriptionPlan[];
  onUpgrade: () => void;
  onCancel?: () => void;
  onResume?: () => void;
}

const AvailablePlans: React.FC<AvailablePlansProps> = ({
  currentSubscription,
  plans,
  onUpgrade,
  onCancel,
  onResume,
}) => {
  // Debug what we're receiving
  // User is on free plan if they have no subscription or if they have a subscription with plan_name='free'
  const isPremium = currentSubscription?.plan_name === "premium";
  const isFree =
    !currentSubscription || currentSubscription?.plan_name === "free";

  const premiumPlan = plans.find((plan) => plan.name === "premium");

  // If there are no plans yet, show a loading state
  if (plans.length === 0) {
    return (
      <div className="text-center py-8">
        <FaSpinner className="animate-spin mx-auto h-8 w-8 text-blue-500 mb-4" />
        <p>Loading subscription plans...</p>
      </div>
    );
  }

  if (!premiumPlan) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>
          Premium plan information is not available. Please try again later.
        </p>
      </div>
    );
  }

  const isSetToCancel = currentSubscription?.cancel_at_period_end === true;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-medium text-gray-800">Available Plans</h2>

      <div className="space-y-4">
        {/* Free Plan - Always show */}
        <div className="border rounded-lg p-6 relative">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium">Free Plan</h3>
              <p className="text-gray-600 mt-1">
                Basic features for personal use
              </p>
            </div>
          </div>

          {/* Current Plan Badge for Free Plan */}
          {isFree && (
            <div className="absolute right-4 top-4">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                Current Plan
              </span>
            </div>
          )}

          <ul className="mt-4 space-y-2">
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">
                Limited alerts (Payment Due Date Reminder only)
              </span>
            </li>
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">Basic spending insights</span>
            </li>
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">Track up to 5 credit cards</span>
            </li>
          </ul>

          {isFree && (
            <div className="mt-4">
              <button
                className="w-full py-2 px-4 bg-gray-100 text-gray-700 rounded-md font-medium"
                disabled
              >
                Current Plan
              </button>
            </div>
          )}
        </div>

        {/* Premium Plan */}
        <div
          className={`border rounded-lg p-6 relative ${isPremium ? "" : "bg-blue-50"}`}
        >
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center">
                <h3 className="text-lg font-medium">Premium Plan</h3>
                {!isPremium && (
                  <span className="ml-2 px-2 py-0.5 bg-blue-500 text-white text-xs rounded-md">
                    Recommended
                  </span>
                )}
              </div>
              <p className="text-gray-600 mt-1">
                Full access to all features, including custom alerts and
                additional insights
              </p>
            </div>
            {!isPremium && (
              <div className="text-xl font-semibold text-blue-600">
                $4/month
              </div>
            )}
            {isPremium && !isSetToCancel && (
              <div className="absolute right-4 top-4">
                <span className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                  Current Plan
                </span>
              </div>
            )}
            {isSetToCancel && (
              <div className="absolute right-4 top-4">
                <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                  Cancelling Soon
                </span>
              </div>
            )}
          </div>

          <ul className="mt-4 space-y-2">
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">
                Custom Alerts (0% Interest Period Ending, Spending Insights,
                Payment Due Date Reminders)
              </span>
            </li>
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">
                AI-powered recommendations for balance transfers and spending
              </span>
            </li>
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">
                Advanced notifications and priority support
              </span>
            </li>
            <li className="flex items-start">
              <FaCheck className="text-green-500 mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-600">Unlimited card tracking</span>
            </li>
          </ul>

          {/* Plan Actions */}
          <div className="mt-4">
            {!isPremium && (
              <button
                onClick={onUpgrade}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700"
              >
                Upgrade to Premium
              </button>
            )}

            {isPremium && !isSetToCancel && onCancel && (
              <div className="flex space-x-2">
                <button
                  className="w-full py-2 px-4 bg-gray-100 text-gray-700 rounded-md font-medium"
                  disabled
                >
                  Current Plan
                </button>
                <button
                  onClick={onCancel}
                  className="py-2 px-4 border border-red-300 text-red-600 rounded-md hover:bg-red-50 flex items-center"
                >
                  <FaTimes className="mr-1" size={14} /> Cancel
                </button>
              </div>
            )}

            {isPremium && isSetToCancel && onResume && (
              <div className="flex space-x-2">
                <button
                  onClick={onResume}
                  className="w-full py-2 px-4 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 flex items-center justify-center"
                >
                  <FaUndo className="mr-1" size={14} /> Resume Subscription
                </button>
              </div>
            )}
          </div>

          {isSetToCancel && currentSubscription?.current_period_end && (
            <div className="mt-3 text-sm text-yellow-700 bg-yellow-50 p-2 rounded">
              Your subscription will end on{" "}
              {new Date(
                currentSubscription.current_period_end
              ).toLocaleDateString()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AvailablePlans;
