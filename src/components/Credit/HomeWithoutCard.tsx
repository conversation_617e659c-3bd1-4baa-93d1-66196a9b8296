import React, { useState, useEffect } from "react";
import {
  FaCreditCard,
  FaUniversity,
  FaGift,
  FaBell,
  FaChartPie,
  FaCalendarAlt,
  FaWallet,
  FaExclamationTriangle,
  FaRobot,
  FaBrain,
  FaExchangeAlt,
  FaDollarSign,
  FaSync,
  FaInfoCircle,
} from "react-icons/fa";
import QuilttContainer from "./QuilttContainer";
import { authApi } from "../../services/api";
import { useNavigate } from "react-router-dom";

const HomeWithoutCard: React.FC = () => {
  const [month, setMonth] = useState<string>("May 2023");
  const [showQuiltt, setShowQuiltt] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>("");
  const navigate = useNavigate();
  useEffect(() => {
    // Get user data from auth service
    const user = authApi.getCurrentUser();
    if (user && user.fullName) {
      setUserName(user.fullName.split(" ")[0]); // Get first name only
    }
  }, []);

  const handleQuilttSuccess = () => {
    // Handle successful card connection
    console.log("Card connected successfully");
    setShowQuiltt(false);
    // Reload the page or fetch cards again to reflect the change
    window.location.reload();
  };

  const handleQuilttExit = () => {
    setShowQuiltt(false);
  };

  const handleQuilttError = (error: unknown) => {
    console.error("Quiltt error:", error);
    setShowQuiltt(false);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Welcome Message with detailed instruction */}
      <div className="bg-white rounded-lg shadow-sm p-5 mb-4">
        <div className=" mb-4 flex items-center">
          <div className=" p-2.5 mr-4">
            <svg
              width="48"
              height="48"
              viewBox="0 0 48 48"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0Z"
                fill="#DBEAFE"
              />
              <path
                d="M24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0Z"
                stroke="#E5E7EB"
              />
              <path d="M34 34H14V14H34V34Z" stroke="#E5E7EB" />
              <g clip-path="url(#clip0_177_9831)">
                <path
                  d="M20.1133 31.4883L19.6211 32.6406C18.8906 32.2695 18.2188 31.8125 17.6094 31.2773L18.4961 30.3906C18.9844 30.8164 19.5273 31.1875 20.1133 31.4883ZM15.5859 24.625H14.332C14.3867 25.4531 14.543 26.2539 14.7891 27.0117L15.9531 26.5469C15.7617 25.9336 15.6328 25.2891 15.5859 24.625ZM15.5859 23.375C15.6406 22.6406 15.7891 21.9297 16.0195 21.2617L14.8672 20.7695C14.5742 21.5898 14.3906 22.4648 14.332 23.375H15.5859ZM16.5117 20.1133C16.8164 19.5312 17.1836 18.9883 17.6094 18.4922L16.7227 17.6055C16.1875 18.2148 15.7266 18.8867 15.3594 19.6172L16.5117 20.1133ZM29.5078 30.3906C28.9648 30.8594 28.3594 31.2617 27.707 31.5781L28.1719 32.7422C28.9805 32.3555 29.7266 31.8594 30.3945 31.2734L29.5078 30.3906ZM18.4922 17.6094C19.0352 17.1406 19.6406 16.7383 20.293 16.4219L19.8281 15.2578C19.0195 15.6445 18.2734 16.1406 17.6094 16.7266L18.4922 17.6094ZM31.4883 27.8867C31.1836 28.4688 30.8164 29.0117 30.3906 29.5078L31.2773 30.3945C31.8125 29.7852 32.2734 29.1094 32.6406 28.3828L31.4883 27.8867ZM32.4141 24.625C32.3594 25.3594 32.2109 26.0703 31.9805 26.7383L33.1328 27.2305C33.4258 26.4062 33.6094 25.5312 33.6641 24.6211H32.4141V24.625ZM26.5469 32.0469C25.9336 32.2422 25.2891 32.3672 24.625 32.4141V33.668C25.4531 33.6133 26.2539 33.457 27.0117 33.2109L26.5469 32.0469ZM23.375 32.4141C22.6406 32.3594 21.9297 32.2109 21.2617 31.9805L20.7695 33.1328C21.5938 33.4258 22.4688 33.6094 23.3789 33.6641V32.4141H23.375ZM32.0469 21.4531C32.2422 22.0664 32.3672 22.7109 32.4141 23.375H33.668C33.6133 22.5469 33.457 21.7461 33.2109 20.9883L32.0469 21.4531ZM17.6094 29.5078C17.1406 28.9648 16.7383 28.3594 16.4219 27.707L15.2578 28.1719C15.6445 28.9805 16.1406 29.7266 16.7266 30.3945L17.6094 29.5078ZM24.625 15.5859C25.3594 15.6406 26.0664 15.7891 26.7383 16.0195L27.2305 14.8672C26.4102 14.5742 25.5352 14.3906 24.625 14.332V15.5859ZM21.4531 15.9531C22.0664 15.7578 22.7109 15.6328 23.375 15.5859V14.332C22.5469 14.3867 21.7461 14.543 20.9883 14.7891L21.4531 15.9531ZM31.2773 17.6055L30.3906 18.4922C30.8594 19.0352 31.2617 19.6406 31.582 20.293L32.7461 19.8281C32.3594 19.0195 31.8633 18.2734 31.2773 17.6055ZM29.5078 17.6094L30.3945 16.7227C29.7852 16.1875 29.1133 15.7266 28.3828 15.3594L27.8906 16.5117C28.4688 16.8164 29.0156 17.1836 29.5078 17.6094Z"
                  fill="#2563EB"
                />
                <path
                  d="M24 29.3125C24.6041 29.3125 25.0938 28.8228 25.0938 28.2188C25.0938 27.6147 24.6041 27.125 24 27.125C23.3959 27.125 22.9062 27.6147 22.9062 28.2188C22.9062 28.8228 23.3959 29.3125 24 29.3125Z"
                  fill="#2563EB"
                />
                <path
                  d="M24.3008 26.1875H23.6758C23.418 26.1875 23.207 25.9766 23.207 25.7188C23.207 22.9453 26.2305 23.2227 26.2305 21.5078C26.2305 20.7266 25.5352 19.9375 23.9883 19.9375C22.8516 19.9375 22.2578 20.3125 21.6758 21.0586C21.5234 21.2539 21.2422 21.293 21.043 21.1523L20.5312 20.793C20.3125 20.6406 20.2617 20.332 20.4297 20.1211C21.2578 19.0586 22.2422 18.375 23.9922 18.375C26.0352 18.375 27.7969 19.5391 27.7969 21.5078C27.7969 24.1484 24.7734 23.9883 24.7734 25.7188C24.7695 25.9766 24.5586 26.1875 24.3008 26.1875Z"
                  fill="#2563EB"
                />
              </g>
              <defs>
                <clipPath id="clip0_177_9831">
                  <path d="M14 14H34V34H14V14Z" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div>
            <h2 className="text-lg font-medium text-gray-800">
              Welcome to Stackeasy, {userName || "User"}!
            </h2>
            <p className="text-gray-700">
              Let's get started by setting up your credit cards to track your
              credit usage.
            </p>
          </div>
        </div>

        {/* Feature Cards - adjusted to match the layout in the image */}
        <div className="grid md:grid-cols-3 gap-4 ">
          <div
            style={{
              backgroundColor: "rgba(239, 246, 255, 1)",
            }}
            className="bg-white rounded-lg shadow-sm p-4 cursor-pointer"
            onClick={() => setShowQuiltt(true)}
          >
            <div className="flex items-center">
              <div className="bg-blue-50 rounded-full p-2 mr-3 flex-shrink-0">
                <FaCreditCard className="text-blue-500 text-sm" />
              </div>
              <div>
                <p className="font-medium text-sm">Add Credit Card</p>
                <p className="text-xs text-gray-500">
                  Track your credit usage and due dates
                </p>
              </div>
            </div>
          </div>

          <div
            style={{
              backgroundColor: "rgba(245, 243, 255, 1)",
            }}
            // redirect to bank page
            onClick={() => navigate("/user/bank")}
            className="bg-white rounded-lg shadow-sm p-4 cursor-pointer"
          >
            <div className="flex items-center">
              <div className="bg-purple-50 rounded-full p-2 mr-3 flex-shrink-0">
                <FaUniversity className="text-purple-500 text-sm" />
              </div>
              <div>
                <p className="font-medium text-sm">Link Bank Account</p>
                <p className="text-xs text-gray-500">
                  Set up automatic payments
                </p>
              </div>
            </div>
          </div>

          <div
            style={{
              backgroundColor: "rgba(236, 253, 245, 1)",
            }}
            // redirect to rewards page
            onClick={() => navigate("/user/rewards")}
            className="bg-white rounded-lg shadow-sm p-4 cursor-pointer"
          >
            <div className="flex items-center">
              <div className="bg-green-50 rounded-full p-2 mr-3 flex-shrink-0">
                <FaGift className="text-green-500 text-sm" />
              </div>
              <div>
                <p className="font-medium text-sm">Track Rewards & Benefits</p>
                <p className="text-xs text-gray-500">
                  Monitor your credit health and rewards
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Card Overview - Matching exact content */}
      <div className="mb-6">
        <h2 className="text-base font-medium text-gray-800 mb-3">
          Credit Card Overview
        </h2>
        <div className="bg-white rounded-lg shadow-sm p-8 flex flex-col items-center">
          <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-4">
            <FaCreditCard className="text-gray-400 text-2xl" />
          </div>
          <p className="font-medium text-base mb-1">
            You haven't added any credit cards yet
          </p>
          <p className="text-gray-500 text-sm mb-4 max-w-md text-center">
            Add your cards to track your credit usage, payment due dates, and
            get personalized recommendations.
          </p>
          <button
            className="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm"
            onClick={() => setShowQuiltt(true)}
          >
            + Add Credit Card
          </button>
        </div>
      </div>

      {/* Quiltt Modal */}
      {showQuiltt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className=" rounded-lg max-w-3xl w-full max-h-[90vh] overflow-auto">
            <div className="p-4">
              <QuilttContainer
                onSuccess={handleQuilttSuccess}
                onExit={handleQuilttExit}
                onError={handleQuilttError}
              />
            </div>
          </div>
        </div>
      )}

      {/* Credit Stats - Three columns with the exact metrics from the image */}
      <div className="grid md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center justify-between mb-2 ">
            <p className="text-sm font-medium">Available Credit</p>
            <div className="bg-blue-50 rounded-md p-1.5 mr-2">
              <FaWallet className="text-blue-500 text-sm" />
            </div>
          </div>
          <div className="flex flex-col">
            <p className="text-blue-600 text-2xl font-bold">$0</p>
            <p className="text-xs text-gray-500">
              Add your cards to see your total available credit
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Utilized Credit</p>
            <div className="bg-orange-50 rounded-md p-1.5 mr-2">
              <FaDollarSign className="text-orange-500 text-sm" />
            </div>
          </div>
          <div className="flex flex-col">
            <p className="text-orange-600 text-2xl font-bold">$0</p>
            <p className="text-xs text-gray-500">
              Track your credit utilization ratio
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Credit Utilization</p>
            <div className="bg-green-50 rounded-md p-1.5 mr-2">
              <FaChartPie className="text-green-500 text-sm" />
            </div>
          </div>
          <div>
            <p
              style={{
                color: "rgba(156, 163, 175, 1)",
              }}
              className=" text-2xl font-bold"
            >
              N/A
            </p>
            <div className="h-1.5 bg-gray-100 rounded-full mt-1 mb-1">
              <div className="bg-green-500 h-1.5 rounded-full w-0"></div>
            </div>
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>0%</span>
              <span>30%</span>
              <span>100%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Important Alerts - Matching empty state */}
      <div className="mb-6">
        <h2 className="text-base font-medium text-gray-800 mb-3">
          Important Alerts
        </h2>
        <div className="bg-white rounded-lg shadow-sm p-8 flex flex-col items-center">
          <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-4">
            <FaBell className="text-gray-400 text-2xl" />
          </div>
          <p className="font-medium text-base mb-1">No alerts yet</p>
          <p className="text-gray-500 text-sm text-center">
            Keep track of 0% APR offers and payment dates once you add your
            cards.
          </p>
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="mb-6">
        <div className="flex items-center mb-3">
          <h2 className="text-base font-medium text-gray-800">
            AI-Powered Recommendations
          </h2>
          <span className="ml-2 bg-purple-100 text-purple-700 text-xs px-2 py-0.5 rounded-full">
            Premium
          </span>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center">
          <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-4">
            <svg
              className="w-6 h-6 text-gray-400"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13 10V3L4 14H11V21L20 10H13Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <p className="font-medium text-base mb-2 text-center">
            Unlock Personalized AI Recommendations
          </p>
          <p className="text-gray-500 text-sm mb-4 text-center max-w-md">
            Sync your cards to get personalized AI-powered recommendations based
            on spending patterns and card benefits.
          </p>
          <button
            className="bg-purple-600 text-white px-3 py-1.5 rounded-md text-sm"
            onClick={() => setShowQuiltt(true)}
          >
            Sync Your Cards
          </button>
          <p className="text-xs text-gray-500 mt-2 text-center">
            We'll analyze your cards and suggest the best ways to maximize
            rewards
          </p>
        </div>
      </div>

      {/* Additional Resources Section */}
      <div className="mb-6">
        <h2 className="text-base font-medium text-gray-800 mb-3">
          Additional Resources
        </h2>

        <div className="grid md:grid-cols-2 gap-4">
          {/* Calendar */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium text-sm">30-Day Calendar</p>
              <div className="flex items-center text-xs">
                <button className="p-1 text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <span className="mx-2">{month}</span>
                <button className="p-1 text-gray-400 hover:text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="grid grid-cols-7 gap-1 text-center text-xs uppercase font-medium text-gray-400 mb-1">
              <div>Sun</div>
              <div>Mon</div>
              <div>Tue</div>
              <div>Wed</div>
              <div>Thu</div>
              <div>Fri</div>
              <div>Sat</div>
            </div>
            <div className="grid grid-cols-7 gap-1 text-center text-xs">
              <div className="p-1 text-gray-300">28</div>
              <div className="p-1 text-gray-300">29</div>
              <div className="p-1 text-gray-300">30</div>
              <div className="p-2">1</div>
              <div className="p-2">2</div>
              <div className="p-2">3</div>
              <div className="p-2">4</div>
              <div className="p-2">5</div>
              <div className="p-2">6</div>
              <div className="p-2">7</div>
              <div className="p-2">8</div>
              <div className="p-2">9</div>
              <div className="p-2">10</div>
              <div className="p-2">11</div>
              <div className="p-2">12</div>
              <div className="p-2">13</div>
              <div className="p-2">14</div>
              <div className="p-2">15</div>
              <div className="p-2">16</div>
              <div className="p-2">17</div>
              <div className="p-2">18</div>
              <div className="p-2">19</div>
              <div className="p-2">20</div>
              <div className="p-2">21</div>
              <div className="p-2">22</div>
              <div className="p-2">23</div>
              <div className="p-2">24</div>
              <div className="p-2">25</div>
              <div className="p-2">26</div>
              <div className="p-2">27</div>
              <div className="p-2">28</div>
              <div className="p-2">29</div>
              <div className="p-2">30</div>
              <div className="p-2">31</div>
              <div className="p-2 text-gray-300">1</div>
            </div>
            <div className="border-t mt-3 pt-3 flex flex-col sm:flex-row justify-start gap-3 text-xs">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1.5"></div>
                <span className="text-gray-600">Payment Due</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-green-500 mr-1.5"></div>
                <span className="text-gray-600">0% APR Ending</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-1.5"></div>
                <span className="text-gray-600">Balance Transfer</span>
              </div>
            </div>
          </div>

          {/* Balance Transfer and Potential Savings */}
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <div className="flex justify-between items-center mb-1">
                <p className="font-medium text-sm">Balance Transfer</p>
                <span className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-medium">
                  Recommended
                </span>
              </div>

              <p className="text-xs text-gray-500 mb-3">
                Find cards with 0% APR offers and calculate your savings.
              </p>
              <p className="font-medium text-sm text-gray-800">
                Potential Interest Savings
              </p>
              <p
                style={{
                  color: "rgba(156, 163, 175, 1)",
                }}
                className="text-2xl font-bold mb-0.5"
              >
                $0
              </p>
              <p className="text-xs text-gray-500 mb-3">
                Add your cards to see potential savings
              </p>
              <button
                className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 rounded-md text-center font-medium"
                onClick={() => setShowQuiltt(true)}
              >
                Find 0% APR Card
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Ready to take control section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow p-6 mb-6">
        <div className="md:flex items-center">
          <div className="md:w-5/6 mb-4 md:mb-0 text-white">
            <h2 className="text-lg font-bold mb-2">
              Ready to take control of your credit?
            </h2>
            <p className="text-sm mb-4 text-blue-100">
              Add your first credit card and start tracking your credit usage,
              payment dates, <br /> and get personalized recommendations.
            </p>
            <div className="flex gap-2">
              <button
                className="bg-white text-blue-600 px-3 py-1.5 rounded text-sm flex items-center"
                onClick={() => setShowQuiltt(true)}
              >
                <FaCreditCard className="mr-1.5 text-xs" /> Add Credit Card
              </button>
              <button className="bg-blue-500 text-white px-3 py-1.5 rounded text-sm flex items-center border border-blue-400">
                <FaUniversity className="mr-1.5 text-xs" /> Add Bank Account
              </button>
            </div>
          </div>
          <div className="md:w-1/6 flex justify-center">
            <img
              src="/images/credit_footer.png"
              alt="Credit Card Illustration"
              className="h-28"
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-500 py-4">
        © 2023 Stackeasy. All rights reserved.
        <div className="flex justify-center gap-4 mt-2">
          <a href="#" className="hover:text-gray-700">
            Affiliate Disclosure
          </a>
          <a href="#" className="hover:text-gray-700">
            Privacy Policy
          </a>
          <a href="#" className="hover:text-gray-700">
            Terms of Service
          </a>
          <a href="#" className="hover:text-gray-700">
            Contact Support
          </a>
        </div>
      </div>
    </div>
  );
};

export default HomeWithoutCard;
