import React, { useState } from "react";
import {
  FaCreditCard,
  FaTrash,
  FaStar,
  FaRegStar,
  FaSpinner,
} from "react-icons/fa";
import { PaymentMethod, billingApi } from "@/services/api";
import { toast } from "react-toastify";

interface PaymentMethodsListProps {
  paymentMethods: PaymentMethod[];
  onRefresh: () => void;
  isRefreshing?: boolean;
}

const getCardIcon = (brand: string) => {
  switch (brand.toLowerCase()) {
    case "visa":
      return "💳 Visa";
    case "mastercard":
      return "💳 Mastercard";
    case "amex":
      return "💳 American Express";
    case "discover":
      return "💳 Discover";
    default:
      return "💳 Card";
  }
};

const PaymentMethodsList: React.FC<PaymentMethodsListProps> = ({
  paymentMethods,
  onRefresh,
}) => {
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<number | null>(null);

  const handleDelete = async (id: number) => {
    if (
      window.confirm("Are you sure you want to delete this payment method?")
    ) {
      setDeletingId(id);
      try {
        await billingApi.deletePaymentMethod(id);
        toast.success("Payment method deleted successfully");
        onRefresh();
      } catch (error: any) {
        toast.error(error.message || "Failed to delete payment method");
      } finally {
        setDeletingId(null);
      }
    }
  };

  const handleSetDefault = async (id: number) => {
    setSettingDefaultId(id);
    try {
      await billingApi.setDefaultPaymentMethod(id);
      toast.success("Default payment method updated");
      onRefresh();
    } catch (error: any) {
      toast.error(error.message || "Failed to update default payment method");
    } finally {
      setSettingDefaultId(null);
    }
  };

  if (paymentMethods.length === 0) {
    return (
      <div className="text-center py-6 bg-gray-50 rounded-lg border border-gray-200">
        <FaCreditCard className="mx-auto h-10 w-10 text-gray-400 mb-2" />
        <p className="text-gray-500">No payment methods found</p>
        <p className="text-sm text-gray-400 mt-1">
          Add a card to manage your subscription
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {paymentMethods.map((method) => (
        <div
          key={method.id}
          className={`relative flex justify-between items-center p-4 border rounded-lg ${
            method.is_default
              ? "border-blue-300 bg-blue-50"
              : "border-gray-200 bg-white"
          }`}
        >
          <div className="flex items-center">
            <div className="mr-3 text-2xl">
              {getCardIcon(method.card_brand)}
            </div>
            <div>
              <div className="font-medium text-gray-800">
                {method.card_brand.charAt(0).toUpperCase() +
                  method.card_brand.slice(1)}{" "}
                •••• {method.last_four}
              </div>
              <div className="text-sm text-gray-500">
                Expires {method.expiry_month}/{method.expiry_year}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {method.is_default ? (
              <div className="flex items-center text-blue-600">
                <FaStar className="mr-1" /> Default
              </div>
            ) : (
              <button
                onClick={() => handleSetDefault(method.id)}
                disabled={settingDefaultId === method.id}
                className="text-gray-600 hover:text-blue-600 flex items-center"
              >
                {settingDefaultId === method.id ? (
                  <FaSpinner className="animate-spin mr-1" />
                ) : (
                  <FaRegStar className="mr-1" />
                )}
                Make Default
              </button>
            )}

            {!method.is_default && (
              <button
                onClick={() => handleDelete(method.id)}
                disabled={deletingId === method.id}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                {deletingId === method.id ? (
                  <FaSpinner className="animate-spin" />
                ) : (
                  <FaTrash />
                )}
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default PaymentMethodsList;
