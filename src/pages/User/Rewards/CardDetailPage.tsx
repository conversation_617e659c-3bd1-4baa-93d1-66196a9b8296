import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import { Link, useParams } from "react-router-dom";
import {
  FaChevronLeft,
  FaChevronDown,
  FaSearch,
  FaGift,
  FaStar,
  FaTicketAlt,
  FaShieldAlt,
  FaCar,
  FaLock,
  FaClock,
  FaSuitcaseRolling,
  FaLuggageCart,
  FaDollarSign,
  FaPlane,
  FaUtensils,
  FaEllipsisH,
  FaCalendarAlt,
  FaShoppingCart,
  FaRegCreditCard,
  FaRegArrowAltCircleUp,
  FaChevronRight,
  FaPlus,
  FaDumbbell,
  FaRobot,
  FaExclamationTriangle,
  FaUniversity,
  FaListAlt,
  FaHashtag,
  FaTimes,
} from "react-icons/fa";
import { IoRocketSharp } from "react-icons/io5";

// Mock data for all cards
const allCards = [
  {
    id: "chase-sapphire-preferred",
    name: "Chase Sapphire",
    type: "Preferred",
    icon: "visa",
    points: "3,500 pts",
    cashback: null,
    miles: null,
    status: "Selected",
  },
  {
    id: "citi-double-cash",
    name: "Citi Double Cash",
    type: "Card",
    icon: "mastercard",
    points: null,
    cashback: "$200",
    miles: null,
    status: null,
  },
  {
    id: "amex-gold",
    name: "Amex Gold",
    type: "Card",
    icon: "amex",
    points: "1,500 pts",
    cashback: null,
    miles: null,
    status: null,
  },
  {
    id: "discover-it",
    name: "Discover It",
    type: "Cash Back",
    icon: "discover",
    points: null,
    cashback: "$75",
    miles: null,
    status: null,
  },
  {
    id: "capital-one-venture",
    name: "Capital One",
    type: "Venture",
    icon: "visa",
    points: null,
    cashback: null,
    miles: "12,000",
    status: null,
  },
];

const CardDetailPage: React.FC = () => {
  const { cardId } = useParams<{ cardId: string }>();
  const [activeTab, setActiveTab] = useState<
    "rewards" | "transactions" | "payments" | "settings"
  >("transactions");
  const [currentPage, setCurrentPage] = useState(1);
  const transactionsPerPage = 7;
  const paymentsPerPage = 8;

  // Find the current card
  const currentCard =
    allCards.find((card) => card.id === cardId) || allCards[0];

  // Mock data for the card details
  const cardData = {
    id: currentCard.id,
    name: `${currentCard.name} ${currentCard.type}`,
    type: "Card Details",
    number: "**** **** **** 4567",
    expiry: "05/28",
    holder: "ALEX JOHNSON",
    transactions: [
      {
        id: "1",
        merchant: "Amazon",
        website: "amazon.com",
        type: "Purchase",
        amount: -50.0,
        date: "April 10, 2025",
        category: "Shopping",
        status: "Completed",
        icon: <FaShoppingCart className="text-blue-600" />,
        iconBg: "bg-blue-100",
      },
      {
        id: "2",
        merchant: "Uber",
        website: "uber.com",
        type: "Purchase",
        amount: -15.5,
        date: "April 9, 2025",
        category: "Travel",
        status: "Completed",
        icon: <FaCar className="text-purple-600" />,
        iconBg: "bg-purple-100",
      },
      {
        id: "3",
        merchant: "Target",
        website: "target.com",
        type: "Purchase",
        amount: -100.0,
        date: "April 8, 2025",
        category: "Shopping",
        status: "Pending",
        icon: <FaStar className="text-red-600" />,
        iconBg: "bg-red-100",
      },
      {
        id: "4",
        merchant: "Starbucks",
        website: "starbucks.com",
        type: "Purchase",
        amount: -6.75,
        date: "April 7, 2025",
        category: "Dining",
        status: "Completed",
        icon: <FaUtensils className="text-green-600" />,
        iconBg: "bg-green-100",
      },
      {
        id: "5",
        merchant: "Chase Bank",
        website: "Payment",
        type: "Payment",
        amount: 250.0,
        date: "April 5, 2025",
        category: "Payment",
        status: "Completed",
        icon: <FaRegCreditCard className="text-indigo-600" />,
        iconBg: "bg-indigo-100",
      },
      {
        id: "6",
        merchant: "Delta Airlines",
        website: "delta.com",
        type: "Purchase",
        amount: -325.75,
        date: "April 3, 2025",
        category: "Travel",
        status: "Completed",
        icon: <FaPlane className="text-sky-600" />,
        iconBg: "bg-sky-100",
      },
      {
        id: "7",
        merchant: "Apple",
        website: "apple.com",
        type: "Refund",
        amount: 49.99,
        date: "April 2, 2025",
        category: "Shopping",
        status: "Completed",
        icon: <FaRegArrowAltCircleUp className="text-gray-600" />,
        iconBg: "bg-gray-100",
      },
      {
        id: "8",
        merchant: "Netflix",
        website: "netflix.com",
        type: "Subscription",
        amount: -15.99,
        date: "April 1, 2025",
        category: "Entertainment",
        status: "Completed",
        icon: <FaTicketAlt className="text-pink-600" />,
        iconBg: "bg-pink-100",
      },
      {
        id: "9",
        merchant: "Whole Foods",
        website: "wholefoods.com",
        type: "Groceries",
        amount: -75.2,
        date: "March 30, 2025",
        category: "Groceries",
        status: "Completed",
        icon: <FaShoppingCart className="text-orange-600" />,
        iconBg: "bg-orange-100",
      },
      {
        id: "10",
        merchant: "Spotify",
        website: "spotify.com",
        type: "Subscription",
        amount: -9.99,
        date: "March 28, 2025",
        category: "Entertainment",
        status: "Completed",
        icon: <FaTicketAlt className="text-teal-600" />,
        iconBg: "bg-teal-100",
      },
      {
        id: "11",
        merchant: "Gas Station",
        website: "Shell",
        type: "Fuel",
        amount: -45.0,
        date: "March 27, 2025",
        category: "Transportation",
        status: "Completed",
        icon: <FaCar className="text-yellow-600" />,
        iconBg: "bg-yellow-100",
      },
      {
        id: "12",
        merchant: "Restaurant X",
        website: "restaurantx.com",
        type: "Dining",
        amount: -60.5,
        date: "March 25, 2025",
        category: "Dining",
        status: "Completed",
        icon: <FaUtensils className="text-red-500" />,
        iconBg: "bg-red-100",
      },
      {
        id: "13",
        merchant: "Movie Theater",
        website: "amc.com",
        type: "Entertainment",
        amount: -30.0,
        date: "March 22, 2025",
        category: "Entertainment",
        status: "Completed",
        icon: <FaTicketAlt className="text-indigo-500" />,
        iconBg: "bg-indigo-100",
      },
      {
        id: "14",
        merchant: "Bookstore",
        website: "books.com",
        type: "Shopping",
        amount: -25.0,
        date: "March 20, 2025",
        category: "Shopping",
        status: "Completed",
        icon: <FaShoppingCart className="text-lime-600" />,
        iconBg: "bg-lime-100",
      },
      {
        id: "15",
        merchant: "Uber Eats",
        website: "ubereats.com",
        type: "Food Delivery",
        amount: -22.0,
        date: "March 18, 2025",
        category: "Dining",
        status: "Completed",
        icon: <FaUtensils className="text-cyan-600" />,
        iconBg: "bg-cyan-100",
      },
      {
        id: "16",
        merchant: "Online Course",
        website: "course.com",
        type: "Education",
        amount: -99.0,
        date: "March 15, 2025",
        category: "Education",
        status: "Completed",
        icon: <FaStar className="text-amber-600" />,
        iconBg: "bg-amber-100",
      },
      {
        id: "17",
        merchant: "Pharmacy",
        website: "cvs.com",
        type: "Health",
        amount: -12.3,
        date: "March 14, 2025",
        category: "Health",
        status: "Completed",
        icon: <FaPlus className="text-rose-600" />,
        iconBg: "bg-rose-100",
      },
      {
        id: "18",
        merchant: "Gym Membership",
        website: "gym.com",
        type: "Fitness",
        amount: -40.0,
        date: "March 12, 2025",
        category: "Fitness",
        status: "Completed",
        icon: <FaDumbbell className="text-fuchsia-600" />,
        iconBg: "bg-fuchsia-100",
      },
      {
        id: "19",
        merchant: "Coffee Shop",
        website: "coffeeshop.com",
        type: "Dining",
        amount: -4.5,
        date: "March 10, 2025",
        category: "Dining",
        status: "Completed",
        icon: <FaUtensils className="text-emerald-600" />,
        iconBg: "bg-emerald-100",
      },
      {
        id: "20",
        merchant: "Hardware Store",
        website: "homedepot.com",
        type: "Home",
        amount: -67.0,
        date: "March 8, 2025",
        category: "Home",
        status: "Completed",
        icon: <FaShoppingCart className="text-violet-600" />,
        iconBg: "bg-violet-100",
      },
      {
        id: "21",
        merchant: "Utility Bill",
        website: "electric.com",
        type: "Utilities",
        amount: -85.0,
        date: "March 5, 2025",
        category: "Utilities",
        status: "Completed",
        icon: <FaRegCreditCard className="text-stone-600" />,
        iconBg: "bg-stone-100",
      },
      {
        id: "22",
        merchant: "Charity Donation",
        website: "charity.org",
        type: "Donation",
        amount: -50.0,
        date: "March 3, 2025",
        category: "Donation",
        status: "Completed",
        icon: <FaGift className="text-pink-500" />,
        iconBg: "bg-pink-100",
      },
      {
        id: "23",
        merchant: "Parking Fee",
        website: "parking.com",
        type: "Transportation",
        amount: -10.0,
        date: "March 2, 2025",
        category: "Transportation",
        status: "Completed",
        icon: <FaCar className="text-orange-500" />,
        iconBg: "bg-orange-100",
      },
      {
        id: "24",
        merchant: "Concert Tickets",
        website: "ticketmaster.com",
        type: "Entertainment",
        amount: -150.0,
        date: "March 1, 2025",
        category: "Entertainment",
        status: "Completed",
        icon: <FaTicketAlt className="text-red-700" />,
        iconBg: "bg-red-100",
      },
    ],
    rewardsSummary: {
      currentPoints: 3500,
      pointsThisMonth: 350,
      pointsValue: 43.75,
      pointToDollarRatio: 0.0125,
      annualFee: 95,
      nextPaymentDate: "August 15, 2025",
    },
    rewardCategories: [
      {
        name: "Travel",
        details: "Flights, hotels, rental cars",
        multiplier: "3x",
        icon: <FaPlane className="text-blue-500" size={24} />,
      },
      {
        name: "Dining",
        details: "Restaurants, takeout, delivery",
        multiplier: "2x",
        icon: <FaUtensils className="text-blue-500" size={24} />,
      },
      {
        name: "All Other Purchases",
        details: "Everything else",
        multiplier: "1x",
        icon: <FaEllipsisH className="text-blue-500" size={24} />,
      },
    ],
    redemptionOptions: [
      {
        title: "Travel Through Chase Portal",
        description:
          "Points are worth 25% more when redeemed for travel through Chase Ultimate Rewards portal.",
        value: "Best value: 1.25¢ per point",
        icon: <FaPlane className="text-blue-500" size={24} />,
      },
      {
        title: "Transfer to Travel Partners",
        description:
          "Transfer points 1:1 to airline and hotel partners including United, Southwest, and Hyatt.",
        value: "Value varies: 1-2¢+ per point",
        icon: <IoRocketSharp className="text-blue-500" size={24} />,
      },
      {
        title: "Gift Cards",
        description:
          "Redeem for gift cards from popular retailers, restaurants, and more.",
        value: "Value: 1¢ per point",
        icon: <FaGift className="text-blue-500" size={24} />,
      },
      {
        title: "Cash Back",
        description:
          "Redeem points for cash back as a statement credit or direct deposit to your bank account.",
        value: "Value: 1¢ per point",
        icon: <FaDollarSign className="text-blue-500" size={24} />,
      },
    ],
    cardBenefits: [
      {
        title: "Trip Cancellation/Interruption Insurance",
        description:
          "Get reimbursed up to $10,000 per person and $20,000 per trip for pre-paid, non-refundable travel expenses.",
        category: "Travel",
        icon: <FaShieldAlt className="text-white" size={24} />,
      },
      {
        title: "Auto Rental Collision Damage Waiver",
        description:
          "Decline the rental company's collision insurance and charge the entire rental cost to your card.",
        category: "Travel",
        icon: <FaCar className="text-white" size={24} />,
      },
      {
        title: "Purchase Protection",
        description:
          "Covers new purchases for 120 days against damage or theft up to $500 per claim and $50,000 per account.",
        category: "Shopping",
        icon: <FaLock className="text-white" size={24} />,
      },
      {
        title: "Extended Warranty Protection",
        description:
          "Extends the time period of the U.S. manufacturer's warranty by an additional year on eligible warranties of three years or less.",
        category: "Shopping",
        icon: <FaClock className="text-white" size={24} />,
      },
      {
        title: "Lost Luggage Reimbursement",
        description:
          "If you or an immediate family member check or carry on luggage that is damaged or lost by the carrier, you\\'re covered up to $3,000 per passenger.",
        category: "Travel",
        icon: <FaSuitcaseRolling className="text-white" size={24} />,
      },
      {
        title: "Baggage Delay Insurance",
        description:
          "Reimburses you for essential purchases like toiletries and clothing for baggage delays over 6 hours, up to $100 a day for 5 days.",
        category: "Travel",
        icon: <FaLuggageCart className="text-white" size={24} />,
      },
    ],
    specialOffers: [
      {
        title: "DoorDash DashPass Membership",
        description:
          "Free DashPass subscription for a minimum of one year. $0 delivery fees and reduced service fees on eligible orders.",
        status: "Active",
        action: "Activate Now",
        icon: <FaUtensils className="text-orange-500" size={24} />,
      },
      {
        title: "Lyft Ride Credits",
        description:
          "Earn 5x total points on Lyft rides through March 2025. That\\'s 3x points in addition to the 2x points you already earn on travel.",
        status: "Expires soon",
        action: "Learn More",
        icon: <FaCar className="text-purple-500" size={24} />,
      },
    ],
    redemptionHistory: [
      {
        date: "Apr 15, 2025",
        type: "Travel",
        description: "Flight to San Francisco",
        points: "25,000",
        value: "$312.50",
      },
      {
        date: "Mar 22, 2025",
        type: "Cash Back",
        description: "Statement Credit",
        points: "10,000",
        value: "$100.00",
      },
      {
        date: "Feb 10, 2025",
        type: "Gift Card",
        description: "Amazon.com Gift Card",
        points: "5,000",
        value: "$50.00",
      },
    ],
    paymentHistory: {
      aiInsight:
        "To avoid accumulating high interest, pay an additional $200 this month to reduce your balance faster.",
      paymentDue: {
        amount: 100,
        date: "May 15, 2025",
        message:
          "Minimum payment required to keep your account in good standing.",
      },
      payments: [
        {
          id: "1",
          amount: 250.0,
          date: "April 10, 2025",
          type: "Manual Payment",
          status: "Completed",
          method: "Bank Transfer (****1234)",
          action: "View",
        },
        {
          id: "2",
          amount: 100.0,
          date: "March 15, 2025",
          type: "Automatic Payment",
          status: "Completed",
          method: "Direct Debit (****5678)",
          action: "View",
        },
        {
          id: "3",
          amount: 200.0,
          date: "March 5, 2025",
          type: "Manual Payment",
          status: "Failed",
          method: "Credit Card (****9012)",
          action: "Retry",
        },
        {
          id: "4",
          amount: 150.0,
          date: "February 20, 2025",
          type: "Manual Payment",
          status: "Completed",
          method: "Bank Transfer (****1234)",
          action: "View",
        },
        {
          id: "5",
          amount: 100.0,
          date: "February 15, 2025",
          type: "Minimum Payment",
          status: "Completed",
          method: "Direct Debit (****5678)",
          action: "View",
        },
        {
          id: "6",
          amount: 175.0,
          date: "January 25, 2025",
          type: "Manual Payment",
          status: "Completed",
          method: "Bank Transfer (****1234)",
          action: "View",
        },
        {
          id: "7",
          amount: 100.0,
          date: "January 15, 2025",
          type: "Automatic Payment",
          status: "Completed",
          method: "Direct Debit (****5678)",
          action: "View",
        },
        {
          id: "8",
          amount: 300.0,
          date: "January 5, 2025",
          type: "Manual Payment",
          status: "Pending",
          method: "Bank Transfer (****1234)",
          action: "Cancel",
        },
        {
          id: "9",
          amount: 120.0,
          date: "December 10, 2024",
          type: "Manual Payment",
          status: "Completed",
          method: "Bank Transfer (****1234)",
          action: "View",
        },
        {
          id: "10",
          amount: 90.0,
          date: "November 15, 2024",
          type: "Automatic Payment",
          status: "Completed",
          method: "Direct Debit (****5678)",
          action: "View",
        },
        {
          id: "11",
          amount: 210.0,
          date: "November 5, 2024",
          type: "Manual Payment",
          status: "Completed",
          method: "Credit Card (****9012)",
          action: "View",
        },
        {
          id: "12",
          amount: 160.0,
          date: "October 20, 2024",
          type: "Manual Payment",
          status: "Completed",
          method: "Bank Transfer (****1234)",
          action: "View",
        },
      ],
    },
    cardSettings: {
      institution: "Chase Bank",
      cardType: "Personal",
      cardName: "Chase Sapphire Preferred",
      lastFourDigits: "4567",
      paymentDueDate: "2025-05-15",
      balanceTransferEligible: "Yes",
      tags: ["Travel", "Rewards", "Dining"],
    },
  };

  const [tags, setTags] = useState(cardData.cardSettings.tags);
  const [newTag, setNewTag] = useState("");

  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row">
        {/* Left Sidebar - Card List */}
        <div className="w-full md:w-80 md:border-r md:border-gray-200 md:pr-6 mb-6 md:mb-0">
          <div className="mb-6 px-4 md:px-0">
            <h2 className="text-xl font-semibold text-gray-800 mb-1">
              My Cards
            </h2>
            <p className="text-sm text-gray-500">
              Select a card to view details
            </p>

            <div className="relative mt-4 mb-6">
              <input
                type="text"
                placeholder="Search cards"
                className="w-full border border-gray-300 rounded-md pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <FaSearch className="text-gray-400" size={14} />
              </div>
            </div>

            <div className="space-y-2">
              {allCards.map((card) => (
                <Link
                  key={card.id}
                  to={`/user/rewards/cards/${card.id}`}
                  className={`block p-4 rounded-md ${card.id === currentCard.id ? "bg-blue-50 border border-blue-100" : "hover:bg-gray-50"}`}
                >
                  <div className="flex items-center">
                    <div
                      className={`w-10 h-10 rounded-md flex items-center justify-center ${
                        card.icon === "visa"
                          ? "bg-blue-600"
                          : card.icon === "amex"
                            ? "bg-purple-600"
                            : card.icon === "discover"
                              ? "bg-orange-500"
                              : card.icon === "mastercard"
                                ? "bg-amber-500"
                                : "bg-gray-500"
                      } text-white mr-3`}
                    >
                      {card.icon === "visa" && "VI"}
                      {card.icon === "mastercard" && "MC"}
                      {card.icon === "amex" && "AX"}
                      {card.icon === "discover" && "DI"}
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">
                        {card.name}
                      </div>
                      <div className="text-xs text-gray-500">{card.type}</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <div className="text-sm text-gray-600">
                      {card.points && `Rewards: ${card.points}`}
                      {card.cashback && `Cashback: ${card.cashback}`}
                      {card.miles && `Miles: ${card.miles}`}
                    </div>
                    {card.id === currentCard.id ? (
                      <div className="text-sm text-blue-600">Selected</div>
                    ) : (
                      <div className="text-sm text-gray-400">Click to view</div>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 md:ml-8 px-4 md:px-0">
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <Link
                to="/rewards"
                className="text-blue-600 flex items-center text-sm font-medium"
              >
                <FaChevronLeft className="mr-1" size={12} />
                Back to My Cards
              </Link>
            </div>

            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div
                  className={`w-12 h-12 rounded-md flex items-center justify-center ${
                    currentCard.icon === "visa"
                      ? "bg-blue-600"
                      : currentCard.icon === "amex"
                        ? "bg-purple-600"
                        : currentCard.icon === "discover"
                          ? "bg-orange-500"
                          : currentCard.icon === "mastercard"
                            ? "bg-amber-500"
                            : "bg-gray-500"
                  } text-white mr-4`}
                >
                  {currentCard.icon === "visa" && "VI"}
                  {currentCard.icon === "mastercard" && "MC"}
                  {currentCard.icon === "amex" && "AX"}
                  {currentCard.icon === "discover" && "DI"}
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800">
                    {cardData.name}
                  </h1>
                  <p className="text-sm text-gray-500">{cardData.type}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Card Visual */}
          <div className="relative mb-8">
            <div className="rounded-lg bg-blue-600 text-white p-6 w-full md:w-[300px]">
              <div className="absolute top-4 left-4 bg-white bg-opacity-20 p-2 rounded-md">
                <svg
                  className="w-5 h-5 text-white"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M9.5 9.3l-2.8 2.8 2.8 2.8M14.5 9.3l2.8 2.8-2.8 2.8" />
                </svg>
              </div>
              <div className="absolute top-4 right-4 bg-white bg-opacity-20 p-2 rounded-md">
                <svg
                  className="w-5 h-5 text-white"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M8.7 6.9c7.2 7.2 7.2 7.2 0 14.4M15.3 6.9c-7.2 7.2-7.2 7.2 0 14.4" />
                </svg>
              </div>
              <div className="flex justify-center items-center py-8">
                <div className="text-xl tracking-widest">{cardData.number}</div>
              </div>
              <div className="flex justify-between items-end">
                <div>
                  <div className="text-xs opacity-80 mb-1">CARD HOLDER</div>
                  <div className="text-sm">{cardData.holder}</div>
                </div>
                <div>
                  <div className="text-xs opacity-80 mb-1">VALID THRU</div>
                  <div className="text-sm">{cardData.expiry}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <div className="flex flex-col md:flex-row overflow-x-auto md:overflow-visible">
              <button
                className={`py-3 md:pb-4 px-4 md:px-6 text-sm font-medium border-b-2 text-center md:text-left ${
                  activeTab === "rewards"
                    ? "text-blue-600 border-blue-600"
                    : "text-gray-500 border-transparent hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("rewards")}
              >
                Rewards & Benefits
              </button>
              <button
                className={`py-3 md:pb-4 px-4 md:px-6 text-sm font-medium border-b-2 text-center md:text-left ${
                  activeTab === "transactions"
                    ? "text-blue-600 border-blue-600"
                    : "text-gray-500 border-transparent hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("transactions")}
              >
                Transactions
              </button>
              <button
                className={`py-3 md:pb-4 px-4 md:px-6 text-sm font-medium border-b-2 text-center md:text-left ${
                  activeTab === "payments"
                    ? "text-blue-600 border-blue-600"
                    : "text-gray-500 border-transparent hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("payments")}
              >
                Payment History
              </button>
              <button
                className={`py-3 md:pb-4 px-4 md:px-6 text-sm font-medium border-b-2 text-center md:text-left ${
                  activeTab === "settings"
                    ? "text-blue-600 border-blue-600"
                    : "text-gray-500 border-transparent hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("settings")}
              >
                Card Settings
              </button>
            </div>
          </div>

          {/* Transactions Tab Content */}
          {activeTab === "transactions" && (
            <div>
              <div className="mb-5">
                <h2 className="text-xl font-medium text-gray-800 mb-2">
                  Transactions
                </h2>
                <p className="text-sm text-gray-600">
                  View and manage your recent transactions, monitor your
                  spending, and track your payments with this card.
                </p>
              </div>

              <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between mb-5 space-y-4 md:space-y-0">
                <div className="relative max-w-full md:max-w-md w-full">
                  <input
                    type="text"
                    placeholder="Search by merchant, amount, or date"
                    className="w-full border border-gray-300 rounded-md pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <FaSearch className="text-gray-400" size={14} />
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                      <option>Last 30 days</option>
                      <option>Last 60 days</option>
                      <option>Last 90 days</option>
                      <option>This year</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>

                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                      <option>All</option>
                      <option>Purchases</option>
                      <option>Payments</option>
                      <option>Refunds</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>

                  <button className="border border-gray-300 rounded-md px-4 py-2 text-sm flex items-center justify-center space-x-1 hover:bg-gray-50 w-full sm:w-auto">
                    <svg
                      className="w-4 h-4 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                      />
                    </svg>
                    <span>More Filters</span>
                  </button>
                </div>
              </div>

              <div className="mb-5 flex flex-col md:flex-row justify-between items-start md:items-center space-y-2 md:space-y-0">
                <div className="text-sm text-gray-500">
                  Showing{" "}
                  {Math.min(
                    cardData.transactions.length,
                    transactionsPerPage * currentPage - transactionsPerPage + 1
                  )}{" "}
                  to{" "}
                  {Math.min(
                    transactionsPerPage * currentPage,
                    cardData.transactions.length
                  )}{" "}
                  of {cardData.transactions.length} transactions
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Sort by:</span>
                  <div className="relative">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-3 pr-8 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                      <option>Date (Newest first)</option>
                      <option>Date (Oldest first)</option>
                      <option>Amount (High to Low)</option>
                      <option>Amount (Low to High)</option>
                      <option>Merchant (A-Z)</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-3 w-3" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Merchant
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Transaction Type
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Amount
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Date
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Category
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {cardData.transactions
                      .slice(
                        (currentPage - 1) * transactionsPerPage,
                        currentPage * transactionsPerPage
                      )
                      .map((transaction) => (
                        <tr key={transaction.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div
                                className={`flex-shrink-0 h-10 w-10 ${transaction.iconBg} rounded-full flex items-center justify-center`}
                              >
                                {transaction.icon}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {transaction.merchant}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {transaction.website}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {transaction.type}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-red-600">
                            {transaction.amount < 0 ? (
                              <span className="text-red-600">
                                -${Math.abs(transaction.amount).toFixed(2)}
                              </span>
                            ) : (
                              <span className="text-green-600">
                                +\${transaction.amount.toFixed(2)}
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {transaction.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${
                                transaction.category === "Shopping"
                                  ? "bg-blue-100 text-blue-800"
                                  : transaction.category === "Travel"
                                    ? "bg-purple-100 text-purple-800"
                                    : transaction.category === "Dining"
                                      ? "bg-green-100 text-green-800"
                                      : transaction.category === "Payment"
                                        ? "bg-indigo-100 text-indigo-800"
                                        : transaction.category ===
                                            "Entertainment"
                                          ? "bg-pink-100 text-pink-800"
                                          : transaction.category === "Groceries"
                                            ? "bg-orange-100 text-orange-800"
                                            : transaction.category ===
                                                "Transportation"
                                              ? "bg-yellow-100 text-yellow-800"
                                              : transaction.category === "Home"
                                                ? "bg-violet-100 text-violet-800"
                                                : transaction.category ===
                                                    "Utilities"
                                                  ? "bg-stone-100 text-stone-800"
                                                  : transaction.category ===
                                                      "Donation"
                                                    ? "bg-pink-100 text-pink-800"
                                                    : transaction.category ===
                                                        "Education"
                                                      ? "bg-amber-100 text-amber-800"
                                                      : transaction.category ===
                                                          "Health"
                                                        ? "bg-rose-100 text-rose-800"
                                                        : transaction.category ===
                                                            "Fitness"
                                                          ? "bg-fuchsia-100 text-fuchsia-800"
                                                          : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {transaction.category}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${
                                transaction.status === "Completed"
                                  ? "bg-green-100 text-green-800"
                                  : transaction.status === "Pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-red-100 text-red-800"
                              }`}
                            >
                              {transaction.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              {/* Pagination */}
              <div className="mt-6 flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0">
                <div className="text-sm text-gray-600">
                  Showing{" "}
                  {Math.min(
                    cardData.transactions.length,
                    transactionsPerPage * currentPage - transactionsPerPage + 1
                  )}{" "}
                  to{" "}
                  {Math.min(
                    transactionsPerPage * currentPage,
                    cardData.transactions.length
                  )}{" "}
                  of {cardData.transactions.length} transactions
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaChevronLeft className="h-4 w-4 text-gray-600" />
                  </button>
                  {[
                    ...Array(
                      Math.ceil(
                        cardData.transactions.length / transactionsPerPage
                      )
                    ).keys(),
                  ].map((num) => (
                    <button
                      key={num + 1}
                      onClick={() => setCurrentPage(num + 1)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        currentPage === num + 1
                          ? "bg-blue-500 text-white"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      {num + 1}
                    </button>
                  ))}
                  {Math.ceil(
                    cardData.transactions.length / transactionsPerPage
                  ) > 5 &&
                    currentPage <
                      Math.ceil(
                        cardData.transactions.length / transactionsPerPage
                      ) -
                        2 && <span className="px-2 py-1">...</span>}
                  {Math.ceil(
                    cardData.transactions.length / transactionsPerPage
                  ) > 4 &&
                    currentPage <
                      Math.ceil(
                        cardData.transactions.length / transactionsPerPage
                      ) -
                        1 && (
                      <button
                        onClick={() =>
                          setCurrentPage(
                            Math.ceil(
                              cardData.transactions.length / transactionsPerPage
                            )
                          )
                        }
                        className={`px-3 py-1 rounded-md text-sm font-medium ${
                          currentPage ===
                          Math.ceil(
                            cardData.transactions.length / transactionsPerPage
                          )
                            ? "bg-blue-500 text-white"
                            : "text-gray-600 hover:bg-gray-100"
                        }`}
                      >
                        {Math.ceil(
                          cardData.transactions.length / transactionsPerPage
                        )}
                      </button>
                    )}
                  <button
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(
                          prev + 1,
                          Math.ceil(
                            cardData.transactions.length / transactionsPerPage
                          )
                        )
                      )
                    }
                    disabled={
                      currentPage ===
                      Math.ceil(
                        cardData.transactions.length / transactionsPerPage
                      )
                    }
                    className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaChevronRight className="h-4 w-4 text-gray-600" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Other tabs content would go here */}
          {activeTab === "rewards" && (
            <div>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-gray-600 font-medium">
                      Current Points
                    </h3>
                    <div className="bg-blue-100 p-2 rounded-full">
                      <FaGift className="text-blue-500" size={18} />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-gray-800">
                    {cardData.rewardsSummary.currentPoints.toLocaleString()}
                    <span className="text-base font-normal"> points</span>
                  </div>
                  <p className="text-sm text-green-500 mt-1">
                    ↑ {cardData.rewardsSummary.pointsThisMonth} pts this month
                  </p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-gray-600 font-medium">Points Value</h3>
                    <div className="bg-green-100 p-2 rounded-full">
                      <FaDollarSign className="text-green-500" size={18} />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-gray-800">
                    ${cardData.rewardsSummary.pointsValue.toFixed(2)}
                    <span className="text-base font-normal text-gray-500 ml-1">
                      USD
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    1 point = ${cardData.rewardsSummary.pointToDollarRatio} when
                    redeemed for travel
                  </p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-gray-600 font-medium">Annual Fee</h3>
                    <div className="bg-yellow-100 p-2 rounded-full">
                      <FaCalendarAlt className="text-yellow-500" size={18} />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-gray-800">
                    ${cardData.rewardsSummary.annualFee}
                    <span className="text-base font-normal text-gray-500 ml-1">
                      per year
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Next payment: {cardData.rewardsSummary.nextPaymentDate}
                  </p>
                </div>
              </div>

              {/* Rewards Categories */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  Rewards Categories
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {cardData.rewardCategories.map((category, index) => (
                    <div key={index} className="bg-white p-6 rounded-lg shadow">
                      <div className="flex items-start mb-3">
                        <div className="bg-blue-100 p-3 rounded-lg mr-4">
                          {category.icon}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">
                            {category.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {category.details}
                          </p>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-gray-800">
                        {category.multiplier}
                        <span className="text-base font-normal text-gray-500 ml-1">
                          points per $1
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Point Redemption Options */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-1">
                  Point Redemption Options
                </h2>
                <p className="text-sm text-gray-500 mb-4">
                  Different ways to use your Chase Ultimate Rewards points
                </p>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {cardData.redemptionOptions.map((option, index) => (
                    <div key={index} className="bg-white p-6 rounded-lg shadow">
                      <div className="flex items-start mb-3">
                        <div className="bg-blue-100 p-3 rounded-lg mr-4">
                          {option.icon}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">
                            {option.title}
                          </h3>
                          <p className="text-sm text-gray-500 mb-2">
                            {option.description}
                          </p>
                          <p
                            className={`text-sm font-medium ${option.value.includes("Best value") ? "text-green-600" : "text-yellow-600"}`}
                          >
                            {option.value}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Card Benefits */}
              <div className="mb-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 space-y-2 sm:space-y-0">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Chase Sapphire Benefits
                  </h2>
                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full sm:w-auto">
                      <option>All Categories</option>
                      <option>Travel</option>
                      <option>Shopping</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-3 w-3" />
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {cardData.cardBenefits.slice(0, 6).map((benefit, index) => (
                    <div
                      key={index}
                      className="bg-blue-600 text-white p-6 rounded-lg shadow-lg relative"
                    >
                      <div className="absolute top-3 right-3 bg-white bg-opacity-10 px-2 py-1 rounded-full text-xs">
                        {benefit.category}
                      </div>
                      <div className="mb-3">{benefit.icon}</div>
                      <h3 className="font-semibold text-lg mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-sm opacity-90 mb-4">
                        {benefit.description}
                      </p>
                      <Link
                        to="#"
                        className="text-sm font-medium text-white hover:underline"
                      >
                        View Details
                      </Link>
                    </div>
                  ))}
                </div>
                {cardData.cardBenefits.length > 6 && (
                  <div className="text-center mt-6">
                    <button className="px-6 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 text-sm font-medium">
                      View All {cardData.cardBenefits.length} Benefits
                    </button>
                  </div>
                )}
              </div>

              {/* Special Card Offers */}
              <div className="mb-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 space-y-2 sm:space-y-0">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Special Card Offers
                  </h2>
                  <Link
                    to="#"
                    className="text-sm font-medium text-blue-600 hover:underline"
                  >
                    View All Offers
                  </Link>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {cardData.specialOffers.map((offer, index) => (
                    <div
                      key={index}
                      className="bg-white p-6 rounded-lg shadow flex"
                    >
                      <div className="bg-gray-100 p-3 rounded-lg mr-4 flex-shrink-0 h-12 w-12 flex items-center justify-center">
                        {offer.icon}
                      </div>
                      <div className="flex-grow">
                        <div className="flex justify-between items-start">
                          <h3 className="font-semibold text-gray-800 mb-1">
                            {offer.title}
                          </h3>
                          <span
                            className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                              offer.status === "Active"
                                ? "bg-green-100 text-green-700"
                                : "bg-red-100 text-red-700"
                            }`}
                          >
                            {offer.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mb-3">
                          {offer.description}
                        </p>
                        <Link
                          to="#"
                          className="text-sm font-medium text-blue-600 hover:underline"
                        >
                          {offer.action}
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recent Redemption History */}
              <div className="mb-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 space-y-2 sm:space-y-0">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Recent Redemption History
                  </h2>
                  <Link
                    to="#"
                    className="text-sm font-medium text-blue-600 hover:underline"
                  >
                    View All History
                  </Link>
                </div>
                <div className="bg-white shadow rounded-lg overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Type
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Description
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Points
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Value
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cardData.redemptionHistory.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${
                                item.type === "Travel"
                                  ? "bg-blue-100 text-blue-800"
                                  : item.type === "Cash Back"
                                    ? "bg-green-100 text-green-800"
                                    : item.type === "Gift Card"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {item.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                            {item.points}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                            {item.value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === "payments" && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-1">
                  Payment History
                </h2>
                <p className="text-sm text-gray-600">
                  View and manage your past payments, track how much you\'ve
                  paid, and see payment details.
                </p>
              </div>

              {/* AI Insight */}
              <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg mb-6 flex items-start">
                <div className="bg-purple-100 p-2 rounded-full mr-3 flex-shrink-0">
                  <FaRobot className="text-purple-600" size={20} />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-purple-700 mb-0.5">
                    AI Insight:
                  </h3>
                  <p className="text-sm text-purple-600">
                    {cardData.paymentHistory.aiInsight}
                  </p>
                </div>
              </div>

              {/* Payment Due Notification */}
              <div className="bg-yellow-50 border border-yellow-300 p-4 rounded-lg mb-8 flex flex-col sm:flex-row items-start">
                <div className="bg-yellow-100 p-2 rounded-full mr-0 sm:mr-3 mb-2 sm:mb-0 flex-shrink-0">
                  <FaCalendarAlt className="text-yellow-600" size={20} />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-yellow-700 mb-0.5">
                    Payment of $
                    {cardData.paymentHistory.paymentDue.amount.toFixed(2)} is
                    due on {cardData.paymentHistory.paymentDue.date}
                  </h3>
                  <p className="text-xs text-yellow-600">
                    {cardData.paymentHistory.paymentDue.message}
                  </p>
                </div>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between mb-5 space-y-4 md:space-y-0">
                <div className="relative max-w-full md:max-w-sm w-full">
                  <input
                    type="text"
                    placeholder="Search payments"
                    className="w-full border border-gray-300 rounded-md pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <FaSearch className="text-gray-400" size={14} />
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                      <option>Last 30 days</option>
                      <option>Last 60 days</option>
                      <option>Last 90 days</option>
                    </select>
                    <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                  </div>
                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                      <option>All Payment Types</option>
                      <option>Manual Payment</option>
                      <option>Automatic Payment</option>
                      <option>Minimum Payment</option>
                    </select>
                    <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                  </div>
                  <div className="relative w-full sm:w-auto">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-4 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                      <option>All Statuses</option>
                      <option>Completed</option>
                      <option>Pending</option>
                      <option>Failed</option>
                    </select>
                    <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                  </div>
                </div>
              </div>

              {/* Payment Count and Sort By */}
              <div className="mb-4 flex flex-col md:flex-row justify-between items-start md:items-center space-y-2 md:space-y-0">
                <div className="text-sm text-gray-500">
                  Showing{" "}
                  {Math.min(
                    cardData.paymentHistory.payments.length,
                    paymentsPerPage * currentPage - paymentsPerPage + 1
                  )}{" "}
                  to{" "}
                  {Math.min(
                    paymentsPerPage * currentPage,
                    cardData.paymentHistory.payments.length
                  )}{" "}
                  of {cardData.paymentHistory.payments.length} payments
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Sort by:</span>
                  <div className="relative">
                    <select className="appearance-none bg-white border border-gray-300 rounded-md pl-3 pr-8 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                      <option>Date (Newest first)</option>
                      <option>Date (Oldest first)</option>
                      <option>Amount (High to Low)</option>
                      <option>Amount (Low to High)</option>
                    </select>
                    <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                  </div>
                </div>
              </div>

              {/* Payments Table */}
              <div className="bg-white border border-gray-200 rounded-lg overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Payment Amount
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Payment Date
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Payment Type
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Payment Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Payment Method
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {cardData.paymentHistory.payments
                      .slice(
                        (currentPage - 1) * paymentsPerPage,
                        currentPage * paymentsPerPage
                      )
                      .map((payment) => (
                        <tr key={payment.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                            \${payment.amount.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {payment.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span
                              className={`px-2 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${
                                payment.type === "Manual Payment"
                                  ? "bg-blue-100 text-blue-800"
                                  : payment.type === "Automatic Payment"
                                    ? "bg-purple-100 text-purple-800"
                                    : payment.type === "Minimum Payment"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {payment.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span
                              className={`px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${
                                payment.status === "Completed"
                                  ? "bg-green-100 text-green-800"
                                  : payment.status === "Pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : payment.status === "Failed"
                                      ? "bg-red-100 text-red-800"
                                      : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {payment.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {payment.method}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <button className="text-blue-600 hover:text-blue-800 font-medium">
                              {payment.action}
                            </button>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              {/* Pagination for Payments Table*/}
              <div className="mt-6 flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0">
                <div className="text-sm text-gray-600">
                  Showing{" "}
                  {Math.min(
                    cardData.paymentHistory.payments.length,
                    paymentsPerPage * currentPage - paymentsPerPage + 1
                  )}{" "}
                  to{" "}
                  {Math.min(
                    paymentsPerPage * currentPage,
                    cardData.paymentHistory.payments.length
                  )}{" "}
                  of {cardData.paymentHistory.payments.length} payments
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaChevronLeft className="h-4 w-4 text-gray-600" />
                  </button>
                  {[
                    ...Array(
                      Math.ceil(
                        cardData.paymentHistory.payments.length /
                          paymentsPerPage
                      )
                    ).keys(),
                  ]
                    .slice(
                      0,
                      Math.min(
                        3,
                        Math.ceil(
                          cardData.paymentHistory.payments.length /
                            paymentsPerPage
                        )
                      )
                    )
                    .map((num) => (
                      <button
                        key={num + 1}
                        onClick={() => setCurrentPage(num + 1)}
                        className={`px-3 py-1 rounded-md text-sm font-medium ${
                          currentPage === num + 1
                            ? "bg-blue-500 text-white"
                            : "text-gray-600 hover:bg-gray-100"
                        }`}
                      >
                        {num + 1}
                      </button>
                    ))}
                  {Math.ceil(
                    cardData.paymentHistory.payments.length / paymentsPerPage
                  ) > 3 &&
                    currentPage <
                      Math.ceil(
                        cardData.paymentHistory.payments.length /
                          paymentsPerPage
                      ) -
                        1 && (
                      <span className="px-2 py-1 text-gray-600">...</span>
                    )}
                  {Math.ceil(
                    cardData.paymentHistory.payments.length / paymentsPerPage
                  ) > 1 &&
                    currentPage <
                      Math.ceil(
                        cardData.paymentHistory.payments.length /
                          paymentsPerPage
                      ) &&
                    Math.ceil(
                      cardData.paymentHistory.payments.length / paymentsPerPage
                    ) > 3 && (
                      <button
                        onClick={() =>
                          setCurrentPage(
                            Math.ceil(
                              cardData.paymentHistory.payments.length /
                                paymentsPerPage
                            )
                          )
                        }
                        className={`px-3 py-1 rounded-md text-sm font-medium ${
                          currentPage ===
                          Math.ceil(
                            cardData.paymentHistory.payments.length /
                              paymentsPerPage
                          )
                            ? "bg-blue-500 text-white"
                            : "text-gray-600 hover:bg-gray-100"
                        }`}
                      >
                        {Math.ceil(
                          cardData.paymentHistory.payments.length /
                            paymentsPerPage
                        )}
                      </button>
                    )}
                  <button
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(
                          prev + 1,
                          Math.ceil(
                            cardData.paymentHistory.payments.length /
                              paymentsPerPage
                          )
                        )
                      )
                    }
                    disabled={
                      currentPage ===
                      Math.ceil(
                        cardData.paymentHistory.payments.length /
                          paymentsPerPage
                      )
                    }
                    className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaChevronRight className="h-4 w-4 text-gray-600" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "settings" && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-1">
                  Card Settings
                </h2>
                <p className="text-sm text-gray-600">
                  Manage your card details, preferences, and other settings.
                </p>
              </div>

              <div className="bg-white p-6 md:p-8 rounded-lg shadow">
                <form className="space-y-6">
                  <div>
                    <label
                      htmlFor="institution"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Institution
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="institution"
                        id="institution"
                        defaultValue={cardData.cardSettings.institution}
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaUniversity className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="cardType"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Card Type
                    </label>
                    <div className="relative">
                      <select
                        id="cardType"
                        name="cardType"
                        defaultValue={cardData.cardSettings.cardType}
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none pr-10"
                      >
                        <option>Personal</option>
                        <option>Business</option>
                        <option>Other</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaChevronDown className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="cardName"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Card Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="cardName"
                        id="cardName"
                        defaultValue={cardData.cardSettings.cardName}
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaListAlt className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="lastFourDigits"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Last Four Digits
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="lastFourDigits"
                        id="lastFourDigits"
                        defaultValue={cardData.cardSettings.lastFourDigits}
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10"
                        maxLength={4}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaHashtag className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Enter only the last 4 digits of your card number for
                      security.
                    </p>
                  </div>

                  <div>
                    <label
                      htmlFor="paymentDueDate"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Payment Due Date
                    </label>
                    <div className="relative">
                      <input
                        type="date"
                        name="paymentDueDate"
                        id="paymentDueDate"
                        defaultValue={cardData.cardSettings.paymentDueDate}
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaCalendarAlt className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="balanceTransferEligible"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Balance Transfer Eligible
                    </label>
                    <div className="relative">
                      <select
                        id="balanceTransferEligible"
                        name="balanceTransferEligible"
                        defaultValue={
                          cardData.cardSettings.balanceTransferEligible
                        }
                        className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none pr-10"
                      >
                        <option>Yes</option>
                        <option>No</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FaChevronDown className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="tags"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Tags
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {tags.map((tag) => (
                        <span
                          key={tag}
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${tag === "Travel" ? "bg-blue-100 text-blue-800" : tag === "Rewards" ? "bg-purple-100 text-purple-800" : "bg-green-100 text-green-800"}`}
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-1.5 flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none"
                          >
                            <FaTimes className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                    <div className="flex">
                      <input
                        type="text"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add new tag"
                        className="flex-grow border border-gray-300 rounded-l-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                      <button
                        type="button"
                        onClick={handleAddTag}
                        className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <FaPlus className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4">
                    <button
                      type="button"
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
                    >
                      Save Changes
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </UserLayout>
  );
};

export default CardDetailPage;
