import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import BankAccountsList from "./BankAccountsList";
import EmptyBankState from "./EmptyBankState";
import AddBankAccountModal from "./AddBankAccountModal";
import LinkCreditCardModal from "./LinkCreditCardModal";
import { FaFilter, FaDownload } from "react-icons/fa";

const BankPage: React.FC = () => {
  const [hasAccounts, setHasAccounts] = useState(false);
  const [isAddBankModalOpen, setIsAddBankModalOpen] = useState(false);
  const [isLinkCreditCardModalOpen, setIsLinkCreditCardModalOpen] =
    useState(false);
  const [selectedBankAccount, setSelectedBankAccount] = useState<any>(null);

  const handleAddBankAccount = () => {
    setIsAddBankModalOpen(true);
  };

  const handleAddBankSuccess = () => {
    setIsAddBankModalOpen(false);
    setHasAccounts(true);
  };

  const handleLinkCreditCard = (bankAccount: any) => {
    setSelectedBankAccount(bankAccount);
    setIsLinkCreditCardModalOpen(true);
  };

  return (
    <UserLayout>
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-800">
            Your Bank Accounts
          </h2>
          <div className="flex items-center space-x-2">
            {hasAccounts ? (
              <>
                <button className="p-1.5 text-gray-600 hover:text-gray-700 rounded focus:outline-none focus:ring-1 focus:ring-gray-400">
                  <FaFilter size={16} />
                </button>
                <button className="p-1.5 text-gray-600 hover:text-gray-700 rounded focus:outline-none focus:ring-1 focus:ring-gray-400">
                  <FaDownload size={16} />
                </button>
                <div className="relative">
                  <select
                    className="appearance-none bg-white border border-gray-300 rounded-md pl-3 pr-8 py-1.5 text-xs font-medium text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="Accounts"
                  >
                    <option>Accounts</option>
                    <option>All Accounts</option>
                    <option>Active Accounts</option>
                    <option>Inactive Accounts</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                    <svg
                      className="fill-current h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>
              </>
            ) : (
              <button
                onClick={handleAddBankAccount}
                className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1.5 rounded-md font-medium"
              >
                Add bank account
              </button>
            )}
          </div>
        </div>

        {hasAccounts ? (
          <BankAccountsList />
        ) : (
          <EmptyBankState onAddBankAccount={handleAddBankAccount} />
        )}
      </div>

      <AddBankAccountModal
        isOpen={isAddBankModalOpen}
        onClose={() => setIsAddBankModalOpen(false)}
        onSuccess={handleAddBankSuccess}
      />

      {selectedBankAccount && (
        <LinkCreditCardModal
          isOpen={isLinkCreditCardModalOpen}
          onClose={() => setIsLinkCreditCardModalOpen(false)}
          bankAccount={selectedBankAccount}
        />
      )}
    </UserLayout>
  );
};

export default BankPage;
