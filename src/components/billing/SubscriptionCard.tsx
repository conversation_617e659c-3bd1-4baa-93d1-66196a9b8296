import React from "react";
import { Subscription } from "@/services/api";
import {
  FaCrown,
  FaRegCalendarAlt,
  FaCheckCircle,
  FaInfoCircle,
} from "react-icons/fa";

interface SubscriptionCardProps {
  subscription: Subscription | null;
  onUpgrade?: () => void;
  onCancel?: () => void;
  onResume?: () => void;
}

// Format date to readable format
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  subscription,
  onUpgrade,
  onCancel,
  onResume,
}) => {
  // If subscription is null, show a "No subscription" message with an upgrade button
  if (!subscription) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-gray-100 text-gray-600 rounded-full mr-3">
              <FaInfoCircle />
            </div>
            <div>
              <h3 className="text-lg font-semibold">No Active Subscription</h3>
              <p className="text-sm text-gray-600">
                You don't have an active subscription plan
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Free Plan Features:
          </h4>
          <ul className="space-y-1">
            <li className="flex items-start text-sm">
              <FaCheckCircle className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
              <span>Basic dashboard access</span>
            </li>
            <li className="flex items-start text-sm">
              <FaCheckCircle className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
              <span>Limited features and functionality</span>
            </li>
          </ul>
        </div>

        <div className="flex justify-end space-x-3">
          {onUpgrade && (
            <button
              onClick={onUpgrade}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Get Premium
            </button>
          )}
        </div>
      </div>
    );
  }

  const { plan_name, display_name, status, billing_cycle, features, isFree } =
    subscription;

  const isPremium = plan_name === "premium";
  const isActive = status === "active";
  const isCancelled = status === "cancelled";
  const isPastDue = status === "past_due";
  const isSetToCancel = subscription.cancel_at_period_end === true;

  const canUpgrade = !isPremium || !isActive;
  const canCancel = isPremium && isActive && !isSetToCancel;
  const canResume = isPremium && isActive && isSetToCancel;

  return (
    <div
      className={`bg-white p-6 rounded-lg shadow-md ${isPremium ? "border-2 border-blue-500" : ""}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          {isPremium ? (
            <div className="p-2 bg-blue-100 text-blue-600 rounded-full mr-3">
              <FaCrown />
            </div>
          ) : (
            <div className="p-2 bg-gray-100 text-gray-600 rounded-full mr-3">
              <FaCheckCircle />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold">{display_name}</h3>
            <p className="text-sm text-gray-600">
              {billing_cycle === "monthly"
                ? "Monthly Billing"
                : billing_cycle === "yearly"
                  ? "Annual Billing (Save 20%)"
                  : "Free Plan"}
            </p>
          </div>
        </div>

        <div>
          {isActive && !isFree && (
            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
              Active
            </span>
          )}
          {isPastDue && (
            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
              Past Due
            </span>
          )}
          {isCancelled && (
            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
              Cancelled
            </span>
          )}
          {isSetToCancel && (
            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
              Cancelling Soon
            </span>
          )}
        </div>
      </div>

      {isSetToCancel && (
        <div className="flex items-center p-3 mb-4 bg-yellow-50 text-yellow-800 rounded-md text-sm">
          <FaInfoCircle className="flex-shrink-0 mr-2" />
          <p>
            Your subscription will be cancelled on{" "}
            {subscription.current_period_end &&
              formatDate(subscription.current_period_end)}
            . You can still use premium features until then.
          </p>
        </div>
      )}

      {isPremium && subscription.current_period_end && (
        <div className="flex items-center mb-4 text-sm text-gray-600">
          <FaRegCalendarAlt className="mr-2" />
          {isSetToCancel ? "Access until" : "Next billing date"}:{" "}
          {formatDate(subscription.current_period_end)}
        </div>
      )}

      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Features:</h4>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start text-sm">
              <FaCheckCircle className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      <div className="flex justify-end space-x-3">
        {canUpgrade && onUpgrade && (
          <button
            onClick={onUpgrade}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {isPremium ? "Change Plan" : "Upgrade to Premium"}
          </button>
        )}

        {canCancel && onCancel && (
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel Subscription
          </button>
        )}

        {canResume && onResume && (
          <button
            onClick={onResume}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Resume Subscription
          </button>
        )}
      </div>
    </div>
  );
};

export default SubscriptionCard;
