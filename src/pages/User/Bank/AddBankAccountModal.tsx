import React, { useState, useEffect, useCallback } from "react";
import { Modal } from "@/components/Modal/Modal";
import { FaLink } from "react-icons/fa";
import { IoShieldCheckmarkOutline } from "react-icons/io5";
import { QuilttButton, QuilttProvider } from "@quiltt/react";
import { quilttApi } from "@/services/quilttApi";
import { useContexts } from "@/hooks/useContexts";

interface AddBankAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddBankAccountModal: React.FC<AddBankAccountModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useContexts();

  const fetchQuilttToken = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const auth_token = localStorage.getItem("auth_token");
      // Use the quilttApi service to get a token
      const token = await quilttApi.getToken(auth_token || "");
      setToken(token);
    } catch (err: unknown) {
      console.error("Error fetching Quiltt token:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to initialize Quiltt";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchQuilttToken();
  }, [fetchQuilttToken]);

  const handleQuilttLoad = () =>
    console.log("QuilttLink onLoad event triggered");
  const handleQuilttExit = () =>
    console.log("QuilttLink onExit event triggered");
  const handleQuilttExitSuccess = (e: any) => {
    console.log("QuilttLink onExitSuccess event triggered", e);
    onSuccess();
  };
  const handleQuilttEvent = (event: any) => {
    console.log("QuilttLink event:", event.type, event.payload);
    if (event.type === "Connection:Created") {
      // After a connection is created, fetch the accounts
      fetchAccounts();
      onSuccess();
    }
    if (event.type === "Error") {
      console.error("Quiltt error:", event.payload);
    }
  };

  const fetchAccounts = async () => {
    try {
      // We already have a valid Quiltt token at this point from the QuilttProvider
      const accounts = await quilttApi.getAccounts();
      console.log("Fetched accounts:", accounts);
      // You can do something with the accounts here
      // For example, store them in context or state
    } catch (err) {
      console.error("Error fetching accounts:", err);
    }
  };

  if (loading) {
    return (
      <Modal
        isOpen={isOpen}
        title="Add A Bank Account"
        modalCloseClick={onClose}
        modalHeader={true}
        classes={{
          modal: "flex items-center justify-center",
          modalDialog: "w-[450px] max-h-[90vh] h-auto",
          modalContent: "px-4 pb-6",
        }}
      >
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="ml-3 text-gray-700">Loading connection...</p>
        </div>
      </Modal>
    );
  }

  if (error || !token) {
    return (
      <Modal
        isOpen={isOpen}
        title="Add A Bank Account"
        modalCloseClick={onClose}
        modalHeader={true}
        classes={{
          modal: "flex items-center justify-center",
          modalDialog: "w-[450px] max-h-[90vh] h-auto",
          modalContent: "px-4 pb-6",
        }}
      >
        <div className="p-4">
          <p className="text-sm text-gray-600 mb-3">
            {error ||
              "Failed to initialize secure connection. Please try again."}
          </p>
          <button
            onClick={fetchQuilttToken}
            className="w-full mt-2 bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 rounded-md"
          >
            Try Again
          </button>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      title="Add A Bank Account"
      modalCloseClick={onClose}
      modalHeader={true}
      classes={{
        modal: "flex items-center justify-center",
        modalDialog: "w-[450px] max-h-[90vh] h-auto",
        modalContent: "px-4 pb-6",
      }}
    >
      <QuilttProvider token={token}>
        <div>
          <QuilttButton
            connectorId="b1cskrg4zo"
            onLoad={handleQuilttLoad}
            onExit={handleQuilttExit}
            onExitSuccess={handleQuilttExitSuccess}
            onEvent={handleQuilttEvent}
            className="block w-full"
          >
            <div className="border border-gray-200 rounded-lg p-4 mb-6 hover:border-blue-400 cursor-pointer transition-colors">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 mr-4">
                  <FaLink className="text-blue-600 text-xl" />
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-medium text-gray-900">
                    Connect bank
                  </h3>
                  <p className="text-gray-600">
                    Connect securely to your bank account
                  </p>
                </div>
              </div>
            </div>
          </QuilttButton>

          <div className="flex items-center justify-center">
            <IoShieldCheckmarkOutline className="text-gray-400 mr-2" />
            <span className="text-xs text-gray-500">Data access by MX</span>
          </div>
        </div>
      </QuilttProvider>
    </Modal>
  );
};

export default AddBankAccountModal;
