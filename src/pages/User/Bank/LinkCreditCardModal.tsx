import React, { useState } from "react";
import { Modal } from "@/components/Modal/Modal";
import { FaCheckCircle } from "react-icons/fa";

interface CreditCard {
  id: string;
  name: string;
  icon: string;
  type: string;
  availableCredit: number;
  currentBalance: number;
}

interface BankAccount {
  id: string;
  bankName: string;
  bankIcon: string;
  accountType: "Checking" | "Savings";
  lastFourDigits: string;
}

interface LinkCreditCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  bankAccount: BankAccount;
}

const LinkCreditCardModal: React.FC<LinkCreditCardModalProps> = ({
  isOpen,
  onClose,
  bankAccount,
}) => {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [step, setStep] = useState(1);

  // Mock data for credit cards
  const creditCards: CreditCard[] = [
    {
      id: "1",
      name: "Chase Sapphire",
      icon: "CS",
      type: "Travel",
      availableCredit: 5000,
      currentBalance: 3500,
    },
    {
      id: "2",
      name: "Citi Double Cash",
      icon: "CD",
      type: "Cashback",
      availableCredit: 2000,
      currentBalance: 900,
    },
    {
      id: "3",
      name: "Amex Gold",
      icon: "AG",
      type: "Dining",
      availableCredit: 2500,
      currentBalance: 1200,
    },
  ];

  const handleCardSelect = (cardId: string) => {
    setSelectedCard(cardId);
  };

  const handleLinkCard = () => {
    // In a real application, this would call an API to link the card
    onClose();
  };

  const getIconBackground = (icon: string) => {
    if (icon === "CS") return "bg-blue-500";
    if (icon === "CD") return "bg-yellow-500";
    if (icon === "AG") return "bg-purple-500";
    return "bg-gray-500";
  };

  const getBankIconBackground = (bankName: string) => {
    if (bankName.includes("Bank of America")) return "bg-red-500";
    if (bankName.includes("Wells Fargo")) return "bg-yellow-500";
    if (bankName.includes("Chase")) return "bg-blue-500";
    if (bankName.includes("Capital One")) return "bg-red-500";
    return "bg-gray-500";
  };

  const getBadgeBackground = (type: string) => {
    if (type === "Travel") return "bg-blue-100 text-blue-800";
    if (type === "Cashback") return "bg-green-100 text-green-800";
    if (type === "Dining") return "bg-yellow-100 text-yellow-800";
    return "bg-gray-100 text-gray-800";
  };

  return (
    <Modal
      isOpen={isOpen}
      title="Link Credit Card to Bank Account"
      modalCloseClick={onClose}
      modalHeader={true}
      classes={{
        modal: "flex items-center justify-center",
        modalDialog: "w-[600px] max-h-[90vh] h-auto",
        modalContent: "px-6 pb-6",
      }}
    >
      <div className="w-full">
        <p className="text-gray-600 mb-6">
          Select a credit card from the list below to link to your bank account
          for automatic payments.
        </p>

        {step === 1 && (
          <>
            <h4 className="text-md font-medium text-gray-800 mb-3">
              Step 1: Select Credit Card
            </h4>
            <div className="space-y-3 mb-6">
              {creditCards.map((card) => (
                <div
                  key={card.id}
                  className={`border rounded-md p-4 cursor-pointer transition-colors ${
                    selectedCard === card.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-blue-300"
                  }`}
                  onClick={() => handleCardSelect(card.id)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedCard === card.id}
                        onChange={() => handleCardSelect(card.id)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-3"
                      />
                      <div
                        className={`${getIconBackground(card.icon)} text-white w-8 h-8 rounded-md flex items-center justify-center font-medium mr-3`}
                      >
                        {card.icon}
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">
                          {card.name} **** 1234
                        </div>
                        <div className="flex items-center mt-1">
                          <span
                            className={`text-xs px-2 py-0.5 rounded-full ${getBadgeBackground(card.type)}`}
                          >
                            {card.type}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-600">
                        Available Credit:{" "}
                        <span className="font-medium">
                          ${card.availableCredit.toLocaleString()}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Current Balance:{" "}
                        <span className="font-medium">
                          ${card.currentBalance.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => setStep(2)}
                disabled={!selectedCard}
                className={`px-4 py-2 rounded-md font-medium ${
                  selectedCard
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-200 text-gray-500 cursor-not-allowed"
                }`}
              >
                Continue
              </button>
            </div>
          </>
        )}

        {step === 2 && (
          <>
            <h4 className="text-md font-medium text-gray-800 mb-3">
              Step 2: Confirm Bank Account
            </h4>
            <div className="border rounded-md p-4 mb-6">
              <div className="flex items-center">
                <div
                  className={`${getBankIconBackground(bankAccount.bankName)} text-white w-8 h-8 rounded-md flex items-center justify-center font-medium mr-3`}
                >
                  {bankAccount.bankIcon}
                </div>
                <div>
                  <div className="font-medium text-gray-800">
                    {bankAccount.bankName}
                  </div>
                  <div className="text-sm text-gray-600">
                    {bankAccount.accountType} Account (****
                    {bankAccount.lastFourDigits})
                  </div>
                </div>
                <div className="ml-auto bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs flex items-center">
                  <FaCheckCircle className="mr-1" />
                  Verified
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <p className="text-sm text-blue-700">
                You are linking{" "}
                {creditCards.find((c) => c.id === selectedCard)?.name} to{" "}
                {bankAccount.bankName} {bankAccount.accountType} for automatic
                payments.
              </p>
            </div>

            <div className="mb-6">
              <div className="flex items-center mb-1">
                <div className="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                <p className="text-sm text-gray-700">
                  Linking your credit card to your bank account allows you to
                  make automatic payments directly from your bank.
                </p>
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setStep(1)}
                className="px-4 py-2 rounded-md text-gray-700 border border-gray-300 hover:bg-gray-50"
              >
                Back
              </button>
              <button
                onClick={handleLinkCard}
                className="px-4 py-2 rounded-md bg-blue-600 text-white font-medium hover:bg-blue-700"
              >
                Link Credit Card
              </button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default LinkCreditCardModal;
