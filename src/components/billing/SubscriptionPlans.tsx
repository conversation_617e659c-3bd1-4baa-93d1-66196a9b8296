import React, { useState } from "react";
import { SubscriptionPlan, PaymentMethod } from "@/services/api";
import { <PERSON>a<PERSON>rown, FaChevronRight, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSpinner } from "react-icons/fa";

interface SubscriptionPlansProps {
  plans: SubscriptionPlan[];
  paymentMethods: PaymentMethod[];
  onSubscribe: (
    planName: string,
    billingCycle: string,
    paymentMethodId: number
  ) => Promise<void>;
  isProcessing: boolean;
}

// Helper to ensure price is a number and has toFixed method
const formatPrice = (price: any): string => {
  if (price === undefined || price === null) return "0.00";
  const numPrice = typeof price === "number" ? price : parseFloat(price);
  return isNaN(numPrice) ? "0.00" : numPrice.toFixed(2);
};

const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  plans,
  paymentMethods,
  onSubscribe,
  isProcessing,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    null
  );
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    number | null
  >(
    paymentMethods.find((m) => m.is_default)?.id ||
      (paymentMethods.length > 0 ? paymentMethods[0].id : null)
  );

  // Filter out free plan for display - we only want to show premium plans
  const premiumPlans = plans.filter((plan) => plan.name !== "free");

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
  };

  const handleSubmit = async () => {
    if (selectedPlan && selectedPaymentMethod) {
      await onSubscribe(selectedPlan.name, billingCycle, selectedPaymentMethod);
    }
  };

  // Show loading if no premium plans available
  if (premiumPlans.length === 0) {
    return (
      <div className="text-center py-8">
        <FaSpinner className="animate-spin mx-auto h-8 w-8 text-blue-500 mb-4" />
        <p>Loading subscription plans...</p>
      </div>
    );
  }

  // Show payment methods required message if no payment methods
  if (paymentMethods.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <FaCrown className="mx-auto h-10 w-10 text-yellow-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">
          Add a Payment Method First
        </h3>
        <p className="text-gray-600 mb-4">
          You need to add a payment method before you can subscribe to a premium
          plan.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Select a Plan</h3>

        <div className="space-y-4">
          {premiumPlans.map((plan) => (
            <div
              key={plan.id}
              onClick={() => handlePlanSelect(plan)}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedPlan?.id === plan.id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-blue-300"
              }`}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 text-blue-600 rounded-full mr-3">
                    <FaCrown />
                  </div>
                  <div>
                    <h4 className="font-medium">{plan.display_name}</h4>
                    <p className="text-sm text-gray-600">{plan.description}</p>
                  </div>
                </div>
                <FaChevronRight
                  className={
                    selectedPlan?.id === plan.id
                      ? "text-blue-500"
                      : "text-gray-400"
                  }
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedPlan && (
        <>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Select Billing Cycle</h3>

            <div className="grid grid-cols-2 gap-4">
              <div
                onClick={() => setBillingCycle("monthly")}
                className={`p-4 border rounded-lg cursor-pointer transition-colors text-center ${
                  billingCycle === "monthly"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-blue-300"
                }`}
              >
                <h4 className="font-medium">Monthly</h4>
                <p className="text-2xl font-bold mt-2">
                  ${formatPrice(selectedPlan.monthly_price)}/mo
                </p>
                <p className="text-sm text-gray-600 mt-1">Billed monthly</p>
              </div>

              <div
                onClick={() => setBillingCycle("yearly")}
                className={`p-4 border rounded-lg cursor-pointer transition-colors text-center ${
                  billingCycle === "yearly"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-blue-300"
                }`}
              >
                <h4 className="font-medium">Yearly</h4>
                <p className="text-2xl font-bold mt-2">
                  $
                  {formatPrice(
                    parseFloat(formatPrice(selectedPlan.yearly_price)) / 12
                  )}
                  /mo
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  ${formatPrice(selectedPlan.yearly_price)} billed annually
                </p>
                <p className="text-sm font-medium text-green-600 mt-1">
                  Save $
                  {formatPrice(
                    parseFloat(formatPrice(selectedPlan.monthly_price)) * 12 -
                      parseFloat(formatPrice(selectedPlan.yearly_price))
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">
              Select Payment Method
            </h3>

            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedPaymentMethod === method.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-blue-300"
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">
                        {method.card_brand.charAt(0).toUpperCase() +
                          method.card_brand.slice(1)}{" "}
                        •••• {method.last_four}
                      </p>
                      <p className="text-sm text-gray-600">
                        Expires {method.expiry_month}/{method.expiry_year}
                        {method.is_default && (
                          <span className="ml-2 text-blue-600">(Default)</span>
                        )}
                      </p>
                    </div>
                    {selectedPaymentMethod === method.id && (
                      <FaCheck className="text-blue-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Summary</h3>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Plan:</span>
                <span className="font-medium">{selectedPlan.display_name}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Billing Cycle:</span>
                <span className="font-medium">
                  {billingCycle === "monthly" ? "Monthly" : "Yearly"}
                </span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Payment Method:</span>
                <span className="font-medium">
                  {paymentMethods
                    .find((m) => m.id === selectedPaymentMethod)
                    ?.card_brand.charAt(0)
                    .toUpperCase()}
                  {paymentMethods
                    .find((m) => m.id === selectedPaymentMethod)
                    ?.card_brand.slice(1)}{" "}
                  ••••
                  {
                    paymentMethods.find((m) => m.id === selectedPaymentMethod)
                      ?.last_four
                  }
                </span>
              </div>

              <div className="border-t border-gray-200 pt-3 mt-3">
                <div className="flex justify-between font-bold">
                  <span>Total:</span>
                  <span>
                    $
                    {billingCycle === "monthly"
                      ? formatPrice(selectedPlan.monthly_price)
                      : formatPrice(selectedPlan.yearly_price)}
                    {billingCycle === "monthly" ? "/month" : "/year"}
                  </span>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleSubmit}
                disabled={
                  isProcessing || !selectedPlan || !selectedPaymentMethod
                }
                className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isProcessing ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>Subscribe Now</>
                )}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SubscriptionPlans;
