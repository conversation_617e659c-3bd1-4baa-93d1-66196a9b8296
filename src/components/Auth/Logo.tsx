import React from "react";

const Logo: React.FC = () => {
  return (
    <div className="flex items-center justify-center mb-4">
      <span className="text-3xl font-bold text-gray-800">Stackeasy</span>
      <div className="text-blue-600 ml-1 mt-1">
        <svg
          width="21"
          height="20"
          viewBox="0 0 21 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_177_10111)">
            <path
              d="M10.0156 0C10.1953 0 10.375 0.0390625 10.5391 0.113281L17.8945 3.23438C18.7539 3.59766 19.3945 4.44531 19.3906 5.46875C19.3711 9.34375 17.7773 16.4336 11.0469 19.6562C10.3945 19.9688 9.63672 19.9688 8.98438 19.6562C2.25391 16.4336 0.660159 9.34375 0.640628 5.46875C0.636721 4.44531 1.27735 3.59766 2.13672 3.23438L9.4961 0.113281C9.65625 0.0390625 9.83594 0 10.0156 0Z"
              fill="#3B82F6"
            />
          </g>
          <defs>
            <clipPath id="clip0_177_10111">
              <path d="M0.015625 0H20.0156V20H0.015625V0Z" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </div>
    </div>
  );
};

export default Logo;
